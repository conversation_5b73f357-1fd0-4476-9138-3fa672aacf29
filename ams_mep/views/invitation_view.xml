<odoo>
    <record id="invitations_view_form_inherit" model="ir.ui.view">
        <field name="name">ams_vm.invitation_form_inherit</field>
        <field name="model">ams_vm.invitation</field>
        <field name="inherit_id" ref="ams_vm.invitations_view_form"/>
        <field name="arch" type="xml">
            <div name="alert_box" position="inside">
                <div class="alert alert-info" role="alert" invisible="state not in ['pending']">
                    <p>
                        <strong>
                            <span>
                                <i class="fa fa-info-circle" title="Info"></i>
                            </span>
                        </strong>
                        Please fill fields in the form and
                        <strong>
                            <i class="fa fa-search" title="Search"></i>
                            Search
                        </strong>
                        with visitor National or Resident ID if it exist. If it does not exist, click on the
                        <strong>
                            <i class="fa fa-plus" title="Add"></i>
                            Add
                        </strong>
                        button to add a new visitor.
                        <br/>
                        in case you want add extra visitors from visitors tab below you can add multiple visitors.
                    </p>
                </div>
            </div>
            <xpath expr="//page[@name='request_info']" position="attributes">
                <attribute name="string">Host Info</attribute>
            </xpath>

            <xpath expr="//field[@name='duration']" position="attributes">
                <attribute name="invisible">schedule_type == 'multiple'</attribute>
                <attribute name="string">Duration (Hours)</attribute>
            </xpath>
            <xpath expr="//field[@name='duration']" position="after">
                <field name="duration" invisible="schedule_type != 'multiple'" string="Duration (Days)"
                       widget="integer"/>
            </xpath>
            <xpath expr="//button[@name='action_request_approve']" position="attributes">
                <attribute name="string">Request Approval</attribute>
            </xpath>

            <xpath expr="//field[@name='visitor_id']" position="attributes">
                <attribute name="readonly">1</attribute>
            </xpath>
            <xpath expr="//button[@name='action_confirm']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='visitor_id']" position="before">
                <div>
                    <div class="o_row">
                        <label for="visitor_id_number" class="o_form_label oe_edit_only"/>
                        <field name="visitor_id_number" class="o_form_field" readonly="is_readonly"/>
                        <button name="action_search_visitor" type="object" class="btn-sm" icon="fa-search"
                                title="Search Visitor" invisible="is_readonly"/>
                        <button name="action_open_visitor_form" type="object" class="btn-sm" icon="fa-plus"
                                title="Add New Visitor" invisible="visitor_id != False"/>
                    </div>
                    <span class="text-muted text-warning" invisible="not (not visitor_id and is_warning)">
                        ⚠️ please click on add button (+) to complete main visitor information
                    </span>


                </div>


                <!--                <div class="col-12">-->
                <!--                    <span class="text-muted text-warning">please click add button to fill visitor information</span>-->
                <!--                </div>-->
                <!--                <div></div>-->


                <!--                <br/>-->
                <!--                 <span class="text-muted text-warning">please click add button to fill visitor information</span>-->

            </xpath>
            <xpath expr="//group[@name='request_info_main_group']" position="after">
                <div class="alert alert-warning" role="alert" readonly="is_readonly">
                    <field name="information_confirmed" required="1"/>
                    <label string="I acknowledge the accuracy of the attached data. In case of any remarks, please contact the Security Department."
                           for="information_confirmed"
                    />
                </div>
            </xpath>

            <xpath expr="//page[@name='visitors']" position="replace">
                <page string="Extra Visitors" name="visitors">
                    <group invisible="is_readonly">
    <group colspan="2">
        <div class="d-flex align-items-center gap-2">
            <label for="extra_visitor_id_number" class="col-form-label" nolabel="1"/>
            <field name="extra_visitor_id_number" readonly="is_readonly" nolabel="1" class="me-2" style="width:250px;"/>
            <button name="action_search_extra_visitor" type="object" class="btn-sm"
                    icon="fa-search"
                    title="Search Visitor"/>
            <button name="action_open_extra_visitor_form" type="object" class="btn-sm"
                    icon="fa-plus"
                    title="Add New Visitor"/>
            <button name="%(ams_vm.action_invitation_visitor_import_wizard)d" type="action"
                    string="Import from Excel" icon="fa-upload" class="btn-primary"
                    help="Import visitors from Excel file"/>
        </div>
    </group>
</group>


                    <field name="invitation_visitor_ids">
                        <list editable="bottom" create="0">
                            <field name="visitor_id" readonly="1"/>
                            <field name="visitor_id_number" readonly="1"/>
                            <field name="visit_id" optional="hide" readonly="1"/>
                            <field name="state" readonly="1" optional="show"
                                   decoration-bf="state == 'pending'"
                                   decoration-warning="state == 'need_approval'"
                                   decoration-success="state == 'approved'"
                                   decoration-danger="state == 'rejected'"
                                   decoration-info="state == 'confirmed'"
                                   decoration-muted="state == 'cancelled'"/>
                            <field name="email_state" readonly="1" optional="show"
                                   decoration-bf="email_state == 'pending'"
                                   decoration-success="email_state == 'sent'"
                                   decoration-danger="email_state == 'failed'"/>
                            <field name="qr_code" readonly="1" optional="hide"/>

                            <button name="action_confirm" type="object" string="" icon="fa-check"
                                    title="Confirm Visit" invisible="state != 'approved'"
                                    groups="ams_vm.group_security_approval"/>
                            <button name="action_send_invitation_email" type="object" string="" icon="fa-envelope"
                                    title="Send Invitation Email" invisible="state != 'approved'"/>
                            <button name="action_print_badge" type="object" string="" icon="fa-print"
                                    title="Print Invitation" invisible="state  != 'approved'"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="invitation_view_list_inherit" model="ir.ui.view">
        <field name="name">ams_vm.invitation.list_inherit</field>
        <field name="model">ams_vm.invitation</field>
        <field name="inherit_id" ref="ams_vm.invitation_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="attributes">
                <attribute name="string">Invitations</attribute>
            </xpath>
            <!--            <xpath expr="//field[@name='status']" position="attributes">-->
            <!--                <attribute name="string">Progress Status</attribute>-->
            <!--            </xpath>-->

        </field>
    </record>


    <record id="invitations_action" model="ir.actions.act_window">
        <field name="name">Invitations</field>
        <field name="res_model">ams_vm.invitation</field>
        <field name="view_mode">list,form</field>


    </record>


</odoo>
