<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Paper Format for Badge -->
        <record id="paperformat_invitation_badge" model="report.paperformat">
            <field name="name">MEP Invitation Badge Format A4</field>
            <field name="default" eval="True"/>
            <field name="disable_shrinking" eval="True"/>
            <field name="dpi">96</field>
            <field name="format">A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">5</field>
            <field name="margin_bottom">0</field>
            <field name="margin_left">0</field>
            <field name="margin_right">0</field>
        </record>

        <!-- Report Action -->
        <record id="invitation_visitor_report_action" model="ir.actions.report">
            <field name="name">MEP Visitor Invitation Badge</field>
            <field name="model">ams_vm.invitation_visitor</field>
            <field name="report_type">qweb-html</field>
            <field name="report_name">ams_mep.invitation_visitor_report_template</field>
            <field name="report_file">ams_mep.invitation_visitor_report_template</field>
            <field name="print_report_name">'Invitation_Badge- %s' % object.invitation_id.name</field>
            <field name="binding_model_id" ref="model_ams_vm_invitation_visitor"/>
            <field name="binding_type">report</field>
            <field name="paperformat_id" ref="paperformat_invitation_badge"/>

        </record>

        <!-- Report Template -->
        <template id="invitation_visitor_report_template">
            <t t-call="web.basic_layout">
                <style>
                    @page {
                    margin: 0;
                    }

                    body {
                    margin: 5px;
                    direction: rtl;
                    }

                    .badge-container {
                    width: 400px;
                    height: 610px;
                    margin: 10px auto;
                    position: relative;
                    page-break-after: always;
                    background-image: url('/ams_vm/static/src/img/badge_bg.jpg');
                    background-size: cover;
                    background-position: center;


                    }

                    /* Visitor Info */
                    .visitor-info {
                    margin-top: 25px;
                    margin-right: 70px;
                    text-align: right;
                    font-size:14px;
                    font-family: 'DINNextLT';
                    src: url('/ams_mep/static/src/fonts/DINNextLT/dinnextlt-regular.ttf') format('truetype');
                    font-weight: 400;
                    font-style: normal;

                    }


                    .visitor-info .label {
                    font-weight: bold;
                    font-family: 'DINNextLT';
                    src: url('/ams_mep/static/src/fonts/DINNextLT/dinnextlt-regular.ttf') format('truetype');
                    font-weight: 400;
                    font-style: normal;
                    text-align: center;
                    white-space: nowrap; /* Prevent text wrapping */

                    }

                    .qr-code {
                    padding-top: 105px;
                    padding-right: 100px;
                    }


                    .qr-code img {
                    border-radius: 8px;
                    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
                    }

                </style>

                <t t-foreach="docs" t-as="doc">
                    <div class="badge-container">

                        <div class="qr-code">
                            <img t-attf-src="/report/barcode/QR/{{ doc.qr_code }}?&amp;width=200&amp;height=200&amp;quiet=0"
                                 alt="QR Code"/>
                        </div>

                        <div class="visitor-info">
                            <table style="width: 100%; height: 100%; border-spacing: 1pt; border-collapse: separate;"
                                   class="table-borderless">
                                <tr>
                                    <td class="label">الاسم :</td>
                                    <td t-esc="doc.visitor_id.name"/>
                                </tr>
                                <tr>
                                    <td class="label">اسم الجهة:</td>
                                    <td t-esc="doc.visitor_id.organization or ''"/>
                                </tr>
                                <tr>
                                    <td class="label">الموقع:</td>
                                    <td t-esc="doc.invitation_id.access_groups_ids[0].name"/>
                                </tr>
                                <tr>
                                    <td class="label">قاعة الاجتماع:</td>
                                    <td t-esc="doc.invitation_id.room_name"/>
                                </tr>
                                <tr>
                                    <td class="label">تاريخ البداية:</td>
                                    <td t-esc="context_timestamp(doc.invitation_id.start_date).strftime('%Y-%m-%d')"/>
                                </tr>
                                <tr>
                                    <td class="label">مدة الزيارة:</td>
                                    <td t-esc="doc.visit_duration_display"/>
                                </tr>
                                <tr>
                                    <td class="label">تاريخ الانتهاء:</td>
                                    <td t-esc="context_timestamp(doc.invitation_id.end_date).strftime('%Y-%m-%d')"/>
                                </tr>

                                <tr>
                                    <td class="label">مقدم الطلب:</td>
                                    <td t-esc="doc.invitation_id.create_uid.name"/>
                                </tr>
                                <tr>
                                    <td class="label">رقم المرجع:</td>
                                    <td t-esc="doc.invitation_id.name"/>
                                </tr>
                            </table>
                        </div>
                    </div>
                </t>
            </t>
        </template>
    </data>
</odoo>
