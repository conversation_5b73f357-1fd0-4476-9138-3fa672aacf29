# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import base64


class Invitation(models.Model):
    # region ---------------------- Private Attributes --------------------------------
    _name = 'ams_vm.invitation'
    _inherit = ['ams_vm.invitation']
    # endregion

    # region ----------------------  Properties ------------------------------------
    # endregion
    # region ---------------------- Default Methods ------------------------------------
    # endregion

    # region ---------------------- Fields Declaration ---------------------------------
    # region  Basic
    visitor_id_number = fields.Char(string="Main Visitor ID")
    extra_visitor_id_number = fields.Char(string="Extra Visitor ID")
    information_confirmed = fields.Boolean(string="I confirm the accuracy of the data", required=True)
    is_warning = fields.Boolean(string="Warning", default=False)
    # endregion

    # region  Relational
    visitor_id = fields.Many2one('ams_vm.visitor', string='Main Visitor', required=False, compute='_compute_visitor_id',
                                 store=True, ondelete='restrict')

    # endregion

    # region ---------------------- Compute methods ------------------------------------
    @api.depends('visitor_id_number')
    def _compute_visitor_id(self):
        for record in self:
            if record.visitor_id_number:
                visitor = self.env['ams_vm.visitor'].search([('id_number', '=', record.visitor_id_number)], limit=1)
                if visitor:
                    record.visitor_id = visitor.id
                    record.visitor_id.message_subscribe(partner_ids=[self.env.user.partner_id.id])
                    record.is_warning = False
                else:
                    record.is_warning = True
                    record.visitor_id = False


    # endregion

    # region ---------------------- CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- Action Methods -------------------------------------
    def action_search_visitor(self):
        """Search for a visitor by ID number and set visitor_id"""
        for record in self:
            if not record.check_if_visitor_exists():
                raise UserError(
                    _("No visitor found with this ID number, click on add button to add new visitor with this ID number."))

    def check_if_visitor_exists(self):
        for record in self:
            if record.visitor_id_number:
                visitor = self.env['ams_vm.visitor'].search(
                    [('id_number', '=', record.visitor_id_number)], limit=1
                )
                if visitor:
                    record.visitor_id = visitor.id
                    record.visitor_id.message_subscribe(partner_ids=[self.env.user.partner_id.id])
                    return True
                else:
                    return False
            else:
                raise UserError(_("Please enter a valid ID number"))

    def check_if_extra_visitor_exists(self):
        for record in self:
            if record.extra_visitor_id_number:
                visitor = self.env['ams_vm.visitor'].search(
                    [('id_number', '=', record.extra_visitor_id_number)], limit=1
                )
                if visitor:
                    # add new invitation visitor in one2many field
                    extra_visitors_ids = self.invitation_visitor_ids.mapped(
                        'visitor_id').ids if self.invitation_visitor_ids else []
                    if visitor.id not in extra_visitors_ids:
                        new_invitation_visitor = self.env['ams_vm.invitation_visitor'].create({
                            'visitor_id_number': record.extra_visitor_id_number,
                            'invitation_id': record.id,
                            'visitor_id': visitor.id
                        })
                    record.extra_visitor_id_number = False
                    visitor.message_subscribe(partner_ids=[self.env.user.partner_id.id])
                    return True
                else:
                    return False
            else:
                raise UserError(_("Please enter a valid ID number"))

    def action_search_extra_visitor(self):
        """Search for a visitor by ID number and set visitor_id"""
        for record in self:
            if not record.check_if_extra_visitor_exists():
                raise UserError(
                    _("No visitor found with this ID number, click on add button to add new visitor with this ID number."))

    def action_open_visitor_form(self):
        """Open the visitor form view with the ID number pre-filled."""
        if not self.check_if_visitor_exists():
            return {
                'type': 'ir.actions.act_window',
                'name': 'Create Visitor',
                'res_model': 'ams_vm.visitor',
                'view_mode': 'form',
                'view_id': self.env.ref('ams_vm.visitor_view_form').id,
                'target': 'new',
                'context': {
                    'default_id_number': self.visitor_id_number,
                    'is_main_visitor': True,
                    'invitation_id': self.id,
                },
            }


    def action_open_extra_visitor_form(self):
        """Open the visitor form view with the ID number pre-filled."""
        if not self.check_if_extra_visitor_exists():
            return {
                'type': 'ir.actions.act_window',
                'name': 'Create Visitor',
                'res_model': 'ams_vm.visitor',
                'view_mode': 'form',
                'view_id': self.env.ref('ams_vm.visitor_view_form').id,
                'target': 'new',
                'context': {
                    'default_id_number': self.extra_visitor_id_number,
                    'is_main_visitor': False,
                    'invitation_id': self.id,
                },
            }


    def action_request_approve(self):
        self._check_information_confirmed()
        return super(Invitation, self).action_request_approve()


    def action_approve(self):
        self._check_information_confirmed()
        return super(Invitation, self).action_approve()


    # region ---------------------- Business Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    def _check_information_confirmed(self):
        for record in self:
            if not record.information_confirmed:
                raise UserError(_("Please confirm the accuracy of the data."))
    # endregion
