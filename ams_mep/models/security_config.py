# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # Debug Access Control
    debug_admin_only = fields.Boolean(
        string='Restrict Debug to Admins Only',
        config_parameter='ams_mep.debug_admin_only',
        default=True,
        help='Only allow admin users to access debug mode'
    )

    # Rate Limiting
    # enable_rate_limiting = fields.Bo<PERSON>an(
    #     string='Enable Rate Limiting',
    #     config_parameter='ams_mep.enable_rate_limiting',
    #     default=True,
    #     help='Enable rate limiting for failed debug access attempts'
    # )
    #
    # max_failed_attempts = fields.Integer(
    #     string='Max Failed Attempts',
    #     config_parameter='ams_mep.max_failed_attempts',
    #     default=5,
    #     help='Maximum failed attempts before blocking IP'
    # )
    #
    # block_duration_minutes = fields.Integer(
    #     string='Block Duration (Minutes)',
    #     config_parameter='ams_mep.block_duration_minutes',
    #     default=15,
    #     help='How long to block IP after max failed attempts'
    # )
    #
    # # Logging
    # log_debug_attempts = fields.Boolean(
    #     string='Log Debug Attempts',
    #     config_parameter='ams_mep.log_debug_attempts',
    #     default=True,
    #     help='Log all debug access attempts'
    # )
    #
    # log_suspicious_activity = fields.Boolean(
    #     string='Log Suspicious Activity',
    #     config_parameter='ams_mep.log_suspicious_activity',
    #     default=True,
    #     help='Log suspicious request patterns'
    # )
    #
    # # Database Access
    # restrict_db_manager = fields.Boolean(
    #     string='Restrict Database Manager',
    #     config_parameter='ams_mep.restrict_db_manager',
    #     default=True,
    #     help='Add additional logging for database manager access'
    # )
    #
    # # Session Security
    # session_timeout_hours = fields.Integer(
    #     string='Session Timeout (Hours)',
    #     config_parameter='ams_mep.session_timeout_hours',
    #     default=8,
    #     help='Automatic session timeout in hours (requires custom implementation)'
    # )
    #
    # # Password Security
    # enforce_strong_passwords = fields.Boolean(
    #     string='Enforce Strong Passwords',
    #     config_parameter='ams_mep.enforce_strong_passwords',
    #     default=False,
    #     help='Require strong passwords for all users'
    # )
    #
    # min_password_length = fields.Integer(
    #     string='Minimum Password Length',
    #     config_parameter='ams_mep.min_password_length',
    #     default=8,
    #     help='Minimum required password length'
    # )
    #
    # # Access Control
    # restrict_user_creation = fields.Boolean(
    #     string='Restrict User Creation',
    #     config_parameter='ams_mep.restrict_user_creation',
    #     default=True,
    #     help='Only allow admins to create new users'
    # )
    #
    # # Audit & Compliance
    # enable_audit_log = fields.Boolean(
    #     string='Enable Audit Logging',
    #     config_parameter='ams_mep.enable_audit_log',
    #     default=True,
    #     help='Log all user actions for compliance'
    # )
    #
    # retention_days = fields.Integer(
    #     string='Log Retention (Days)',
    #     config_parameter='ams_mep.retention_days',
    #     default=90,
    #     help='How long to keep security logs'
    # )

    @api.model
    def get_security_config(self):
        """Helper method to get security configuration for controllers"""
        ICPSudo = self.env['ir.config_parameter'].sudo()

        return {
            # Debug & Development
            'debug_admin_only': ICPSudo.get_param('ams_mep.debug_admin_only', 'True') == 'True',
            'restrict_db_manager': ICPSudo.get_param('ams_mep.restrict_db_manager', 'True') == 'True',

            # Rate Limiting
            'enable_rate_limiting': ICPSudo.get_param('ams_mep.enable_rate_limiting', 'True') == 'True',
            'max_failed_attempts': int(ICPSudo.get_param('ams_mep.max_failed_attempts', '5')),
            'block_duration_minutes': int(ICPSudo.get_param('ams_mep.block_duration_minutes', '15')),

            # Access Control
            'restrict_user_creation': ICPSudo.get_param('ams_mep.restrict_user_creation', 'True') == 'True',
            'session_timeout_hours': int(ICPSudo.get_param('ams_mep.session_timeout_hours', '8')),

            # Password Security
            'enforce_strong_passwords': ICPSudo.get_param('ams_mep.enforce_strong_passwords', 'False') == 'True',
            'min_password_length': int(ICPSudo.get_param('ams_mep.min_password_length', '8')),

            # Logging & Monitoring
            'log_debug_attempts': ICPSudo.get_param('ams_mep.log_debug_attempts', 'True') == 'True',
            'log_suspicious_activity': ICPSudo.get_param('ams_mep.log_suspicious_activity', 'True') == 'True',
            'enable_audit_log': ICPSudo.get_param('ams_mep.enable_audit_log', 'True') == 'True',
            'retention_days': int(ICPSudo.get_param('ams_mep.retention_days', '90')),
        }


class SecurityEvent(models.Model):
    _name = 'ams_mep.security.event'
    _description = 'Security Event Log'
    _order = 'create_date desc'
    _rec_name = 'event_type'

    event_type = fields.Selection([
        ('debug_access_denied', 'Debug Access Denied'),
        ('debug_access_granted', 'Debug Access Granted'),
        ('suspicious_activity', 'Suspicious Activity'),
        ('rate_limit_exceeded', 'Rate Limit Exceeded'),
        ('db_manager_access', 'Database Manager Access'),
        ('failed_login', 'Failed Login'),
        ('successful_login', 'Successful Login'),
    ], string='Event Type', required=True)
    
    user_id = fields.Many2one('res.users', string='User')
    user_login = fields.Char(string='User Login')
    client_ip = fields.Char(string='Client IP', required=True)
    user_agent = fields.Text(string='User Agent')
    request_url = fields.Char(string='Request URL')
    description = fields.Text(string='Description')
    severity = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ], string='Severity', default='medium')
    
    # Additional context
    session_id = fields.Char(string='Session ID')
    referer = fields.Char(string='Referer')
    
    @api.model
    def log_security_event(self, event_type, client_ip, user_id=None, user_login=None, 
                          request_url=None, description=None, severity='medium', **kwargs):
        """Helper method to log security events"""
        try:
            vals = {
                'event_type': event_type,
                'client_ip': client_ip,
                'user_id': user_id,
                'user_login': user_login,
                'request_url': request_url,
                'description': description,
                'severity': severity,
            }
            vals.update(kwargs)
            
            return self.sudo().create(vals)
        except Exception as e:
            # Don't fail the main process if logging fails
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error("Failed to log security event: %s", str(e))
            return False
