# -*- coding: utf-8 -*-

import logging
import re
from datetime import datetime, timedelta
from odoo import http, _, fields
from odoo.addons.web.controllers.webclient import WebClient
from odoo.http import request, Response
from odoo.addons.web.controllers.home import Home
from odoo.exceptions import AccessError, UserError
from werkzeug.exceptions import Forbidden, NotFound
_logger = logging.getLogger(__name__)

# Simple in-memory rate limiting (for production, use Redis or database)
_failed_attempts = {}
_blocked_ips = {}

# Configuration
MAX_FAILED_ATTEMPTS = 5
BLOCK_DURATION_MINUTES = 15
SUSPICIOUS_PATTERNS = [
    r'debug=1',
    r'debug=true',
    r'debug=assets',
    r'debug=tests',
    r'__debug__',
    r'developer',
    r'dev_mode',
]


def _get_client_ip():
    """Get client IP address, considering proxy headers"""
    # Check for forwarded IP (when behind proxy)
    forwarded_for = request.httprequest.environ.get('HTTP_X_FORWARDED_FOR')
    if forwarded_for:
        return forwarded_for.split(',')[0].strip()

    # Check for real IP (when behind proxy)
    real_ip = request.httprequest.environ.get('HTTP_X_REAL_IP')
    if real_ip:
        return real_ip.strip()

    # Fallback to remote address
    return request.httprequest.environ.get('REMOTE_ADDR', 'unknown')


def _is_ip_blocked(ip):
    """Check if IP is currently blocked"""
    try:
        # Get security configuration to check if rate limiting is enabled
        from odoo.http import request
        config = request.env['res.config.settings'].get_security_config()

        if not config.get('enable_rate_limiting', True):
            # Rate limiting is disabled, no IPs are blocked
            return False

        # Get dynamic block duration
        block_duration = config.get('block_duration_minutes', BLOCK_DURATION_MINUTES)

        if ip in _blocked_ips:
            block_time = _blocked_ips[ip]
            if datetime.now() - block_time < timedelta(minutes=block_duration):
                return True
            else:
                # Block expired, remove it
                del _blocked_ips[ip]
                _logger.info("IP %s block expired after %d minutes", ip, block_duration)
        return False

    except Exception as e:
        # Fallback to static configuration if dynamic config fails
        _logger.error("Error checking IP block status: %s", str(e))
        if ip in _blocked_ips:
            block_time = _blocked_ips[ip]
            if datetime.now() - block_time < timedelta(minutes=BLOCK_DURATION_MINUTES):
                return True
            else:
                del _blocked_ips[ip]
        return False


def _record_failed_attempt(ip):
    """Record a failed attempt and block IP if necessary"""
    try:
        # Get security configuration to check if rate limiting is enabled
        from odoo.http import request
        config = request.env['res.config.settings'].get_security_config()

        if not config.get('enable_rate_limiting', True):
            # Rate limiting is disabled, just log the attempt
            _logger.info("Failed attempt from IP %s (rate limiting disabled)", ip)
            return False

        # Get dynamic configuration values
        max_attempts = config.get('max_failed_attempts', MAX_FAILED_ATTEMPTS)
        block_duration = config.get('block_duration_minutes', BLOCK_DURATION_MINUTES)

        now = datetime.now()

        # Clean old attempts (older than block duration)
        if ip in _failed_attempts:
            _failed_attempts[ip] = [
                attempt_time for attempt_time in _failed_attempts[ip]
                if now - attempt_time < timedelta(minutes=block_duration)
            ]
        else:
            _failed_attempts[ip] = []

        # Add current attempt
        _failed_attempts[ip].append(now)

        # Check if should block
        if len(_failed_attempts[ip]) >= max_attempts:
            _blocked_ips[ip] = now
            _logger.warning("IP %s blocked due to %d failed attempts (max: %d)",
                           ip, len(_failed_attempts[ip]), max_attempts)

            # Log security event
            try:
                request.env['ams_mep.security.event'].sudo().log_security_event(
                    event_type='rate_limit_exceeded',
                    client_ip=ip,
                    description=f"IP blocked after {len(_failed_attempts[ip])} failed attempts",
                    severity='high'
                )
            except:
                pass  # Don't fail if logging fails

            return True

        return False

    except Exception as e:
        # Fallback to static configuration if dynamic config fails
        _logger.error("Error in rate limiting configuration: %s", str(e))
        now = datetime.now()

        if ip in _failed_attempts:
            _failed_attempts[ip] = [
                attempt_time for attempt_time in _failed_attempts[ip]
                if now - attempt_time < timedelta(minutes=BLOCK_DURATION_MINUTES)
            ]
        else:
            _failed_attempts[ip] = []

        _failed_attempts[ip].append(now)

        if len(_failed_attempts[ip]) >= MAX_FAILED_ATTEMPTS:
            _blocked_ips[ip] = now
            _logger.warning("IP %s blocked due to %d failed attempts (fallback)", ip, len(_failed_attempts[ip]))
            return True

        return False


def _check_suspicious_request(request_url, query_string):
    """Check if request contains suspicious patterns"""
    full_url = f"{request_url}?{query_string}" if query_string else request_url

    for pattern in SUSPICIOUS_PATTERNS:
        if re.search(pattern, full_url, re.IGNORECASE):
            return True
    return False


class RestrictedHome(Home):
    """
    Security controller to restrict debug access to admin users only.
    This addresses penetration test finding 4.3 - Debugging Information Disclosure.
    """

    @http.route()
    def web_client(self, s_action=None, **kw):
        """
        Simple debug access control - only admin users can access debug mode.
        Addresses penetration test finding 4.3 - Debugging Information Disclosure.
        """
        # Check if debug parameter is present in URL or kw
        debug_param = kw.get('debug') or request.httprequest.args.get('debug')

        if debug_param:
            try:
                # Get security configuration
                config = request.env['res.config.settings'].get_security_config()

                # Check if debug admin restriction is enabled
                if config.get('debug_admin_only', True):
                    # Ensure user is authenticated and is admin
                    if not request.session.uid:
                        _logger.warning("Unauthorized debug access attempt from IP: %s",
                                      _get_client_ip())
                        # Redirect to web client without debug parameter
                        base_url = '/odoo'
                        if s_action:
                            base_url += f'#{s_action}'
                        return request.redirect(base_url)
                    else:
                        user = request.env['res.users'].sudo().browse(request.session.uid)
                        if not user._is_admin():
                            _logger.warning("Non-admin user %s attempted debug access from IP: %s",
                                          user.login, _get_client_ip())
                            # Redirect to web client without debug parameter
                            base_url = '/odoo'
                            if s_action:
                                base_url += f'#{s_action}'
                            #return request.redirect(base_url)
                            return  Forbidden()
                        else:
                            _logger.info("Admin user %s accessed debug mode from IP: %s",
                                       user.login, _get_client_ip())
                            # Allow debug for admin users - continue normally
                # If debug_admin_only is disabled, allow debug for all users

            except Exception as e:
                _logger.error("Error checking debug access permissions: %s", str(e))
                # Redirect to web client without debug parameter on error
                base_url = '/web'
                if s_action:
                    base_url += f'#{s_action}'
                return request.redirect(base_url)

        return super().web_client(s_action, **kw)


    # ==================================================================================
    # ARCHIVED: Complex web_client method with full security features
    # ==================================================================================
    def web_client_full_security(self, **kw):
        """
        ARCHIVED: Full-featured web client override with comprehensive security.
        Includes rate limiting, suspicious activity detection, and detailed logging.

        This method was archived in favor of the simpler debug_admin_only implementation.
        Can be restored by replacing the current web_client method if needed.

        Features included:
        - IP blocking and rate limiting
        - Suspicious request pattern detection
        - Comprehensive security event logging
        - Configurable security parameters
        - Detailed audit trails
        """
        client_ip = _get_client_ip()

        # Check if IP is blocked
        if _is_ip_blocked(client_ip):
            _logger.warning("Blocked IP %s attempted to access web client", client_ip)
            return request.render('http_routing.http_error', {
                'status_code': 429,
                'status_message': 'Too Many Requests',
                'error_message': 'Your IP has been temporarily blocked due to suspicious activity.'
            })

        # Check for suspicious patterns in the request
        request_url = request.httprequest.path
        query_string = request.httprequest.query_string.decode('utf-8') if request.httprequest.query_string else ''

        if _check_suspicious_request(request_url, query_string):
            # Get security configuration to check if suspicious activity logging is enabled
            try:
                config = request.env['res.config.settings'].get_security_config()
                if config.get('log_suspicious_activity', True):
                    _logger.warning("Suspicious request detected from IP %s: %s?%s",
                                   client_ip, request_url, query_string)

                    # Log security event
                    try:
                        request.env['ams_mep.security.event'].sudo().log_security_event(
                            event_type='suspicious_activity',
                            client_ip=client_ip,
                            request_url=f"{request_url}?{query_string}" if query_string else request_url,
                            description=f"Suspicious request pattern detected: {request_url}",
                            severity='medium'
                        )
                    except:
                        pass  # Don't fail if logging fails
            except Exception as e:
                # Fallback to basic logging if config fails
                _logger.warning("Suspicious request detected from IP %s: %s?%s (config error: %s)",
                               client_ip, request_url, query_string, str(e))

        # Check if debug parameter is present
        if kw.get('debug'):
            try:
                # Get security configuration
                config = request.env['res.config.settings'].get_security_config()

                # Check if debug admin restriction is enabled
                if config.get('debug_admin_only', True):
                    # Ensure user is authenticated
                    if not request.session.uid:
                        _logger.warning("Unauthorized debug access attempt from IP: %s", client_ip)
                        _record_failed_attempt(client_ip)
                        kw.pop('debug')  # Remove debug parameter

                        # Log security event
                        try:
                            request.env['ams_mep.security.event'].sudo().log_security_event(
                                event_type='debug_access_denied',
                                client_ip=client_ip,
                                description='Unauthenticated debug access attempt',
                                severity='high',
                                request_url=request_url
                            )
                        except:
                            pass  # Don't fail if logging fails
                    else:
                        # Check if user is admin
                        user = request.env['res.users'].sudo().browse(request.session.uid)
                        if not user._is_admin():
                            _logger.warning("Non-admin user %s (ID: %s) attempted debug access from IP: %s",
                                          user.login, user.id, client_ip)
                            _record_failed_attempt(client_ip)
                            kw.pop('debug')  # Remove debug parameter for non-admin users

                            # Log security event
                            try:
                                request.env['ams_mep.security.event'].sudo().log_security_event(
                                    event_type='debug_access_denied',
                                    client_ip=client_ip,
                                    user_id=user.id,
                                    user_login=user.login,
                                    description=f"Non-admin user {user.login} attempted debug access",
                                    severity='medium',
                                    request_url=request_url
                                )
                            except:
                                pass  # Don't fail if logging fails
                        else:
                            _logger.info("Admin user %s (ID: %s) accessed debug mode from IP: %s",
                                       user.login, user.id, client_ip)

                            # Log successful admin debug access
                            try:
                                request.env['ams_mep.security.event'].sudo().log_security_event(
                                    event_type='debug_access_granted',
                                    client_ip=client_ip,
                                    user_id=user.id,
                                    user_login=user.login,
                                    description=f"Admin user {user.login} accessed debug mode",
                                    severity='low',
                                    request_url=request_url
                                )
                            except:
                                pass  # Don't fail if logging fails
                else:
                    # Debug admin restriction is disabled, allow debug for all authenticated users
                    if request.session.uid:
                        user = request.env['res.users'].sudo().browse(request.session.uid)
                        _logger.info("User %s (ID: %s) accessed debug mode from IP: %s (admin restriction disabled)",
                                   user.login, user.id, client_ip)
                    else:
                        _logger.warning("Unauthenticated debug access from IP: %s (admin restriction disabled)", client_ip)

            except Exception as e:
                _logger.error("Error checking debug access permissions: %s", str(e))
                kw.pop('debug', None)  # Remove debug parameter on error

        return super().web_client(**kw)

    # @http.route('/web/database/manager', type='http', auth="none")
    # def database_manager(self, **kw):
    #     """
    #     Override database manager route to add additional security logging.
    #     This addresses penetration test finding 4.4 - Information Disclosure.
    #     """
    #     client_ip = _get_client_ip()
    #
    #     try:
    #         # Get security configuration to check if database manager logging is enabled
    #         config = request.env['res.config.settings'].get_security_config()
    #
    #         if config.get('restrict_db_manager', True):
    #             _logger.warning("Database manager access attempt from IP: %s", client_ip)
    #
    #             # Log security event
    #             try:
    #                 request.env['ams_mep.security.event'].sudo().log_security_event(
    #                     event_type='db_manager_access',
    #                     client_ip=client_ip,
    #                     request_url='/web/database/manager',
    #                     description='Database manager access attempt',
    #                     severity='high'
    #                 )
    #             except:
    #                 pass  # Don't fail if logging fails
    #         else:
    #             _logger.info("Database manager access from IP: %s (logging disabled)", client_ip)
    #
    #     except Exception as e:
    #         # Fallback to basic logging if config fails
    #         _logger.warning("Database manager access attempt from IP: %s (config error: %s)",
    #                        client_ip, str(e))
    #
    #     return super().database_manager(**kw)

    # @http.route(['/web/database/selector', '/web/database/list'], type='http', auth="none")
    # def database_selector(self, **kw):
    #     """
    #     Override database selector to add security logging.
    #     """
    #     # Log database selector access attempts
    #     _logger.info("Database selector access from IP: %s",
    #                 request.httprequest.environ.get('REMOTE_ADDR'))
    #
    #     return super().database_selector(**kw)

    def web_tests(self, **kw):
        # Block non-admin users
        if not request.env.user.has_group('base.group_system'):
            return request.redirect('/web?error=access_denied', 302)
        return super().web_tests(**kw)

class RestrictWebClient(WebClient):

    @http.route()
    def unit_tests_suite(self, mod=None, **kwargs):
        # Block non-admin users
        user = request.env['res.users'].sudo().browse(request.session.uid)

        if not user._is_admin():
            _logger.warning("Unauthorized unit tests access attempt by user '%s' (ID: %s) from IP: %s",
                           user.login, user.id, _get_client_ip())
            raise AccessError(_("Access Denied: Unit tests are restricted to administrators only."))

        return super().unit_tests_suite(mod, **kwargs)

class SecurityController(http.Controller):
    """
    Additional security controller for monitoring and logging.
    """

    # @http.route('/web/session/authenticate', type='json', auth="none")
    # def authenticate(self, db, login, password, base_location=None):
    #     """
    #     Override authentication to add security logging.
    #     This helps with monitoring for brute force attacks.
    #     """
    #     try:
    #         # Get client IP
    #         client_ip = request.httprequest.environ.get('REMOTE_ADDR')
    #
    #         # Call original authentication
    #         result = request.session.authenticate(db, login, password)
    #
    #         if result:
    #             _logger.info("Successful login for user '%s' from IP: %s", login, client_ip)
    #         else:
    #             _logger.warning("Failed login attempt for user '%s' from IP: %s", login, client_ip)
    #
    #         return result
    #
    #     except Exception as e:
    #         client_ip = request.httprequest.environ.get('REMOTE_ADDR')
    #         _logger.error("Authentication error for user '%s' from IP: %s - Error: %s",
    #                      login, client_ip, str(e))
    #         return False

    # @http.route('/web/session/destroy', type='json', auth="user")
    # def destroy(self):
    #     """
    #     Override session destroy to add security logging.
    #     """
    #     user = request.env.user
    #     client_ip = request.httprequest.environ.get('REMOTE_ADDR')
    #
    #     _logger.info("User '%s' (ID: %s) logged out from IP: %s",
    #                 user.login, user.id, client_ip)
    #
    #     return request.session.logout()

    @http.route('/web/debug', type='http', auth="user")
    def debug_access(self, **kw):
        """
        Custom route to handle debug access requests.
        Only allows access for admin users.
        """
        user = request.env.user
        client_ip = request.httprequest.environ.get('REMOTE_ADDR')
        
        if not user._is_admin():
            _logger.warning("Unauthorized debug access attempt by user '%s' (ID: %s) from IP: %s", 
                           user.login, user.id, client_ip)
            raise AccessError(_("Access Denied: Debug mode is restricted to administrators only."))
        
        _logger.info("Debug access granted to admin user '%s' (ID: %s) from IP: %s", 
                    user.login, user.id, client_ip)
        
        # Redirect to web client with debug enabled
        return request.redirect('/web?debug=1')

    @http.route('/web/security/status', type='json', auth="user")
    def security_status(self):
        """
        Endpoint to check current user's security permissions.
        Useful for frontend security checks.
        """
        user = request.env.user
        
        return {
            'is_admin': user._is_admin(),
            'debug_allowed': user._is_admin(),
            'user_id': user.id,
            'login': user.login,
            'groups': [group.name for group in user.groups_id],
        }
