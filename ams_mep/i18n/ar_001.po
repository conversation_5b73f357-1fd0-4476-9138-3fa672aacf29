# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ams_mep
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-11 03:56+0000\n"
"PO-Revision-Date: 2025-03-11 03:56+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ams_mep
#: model:ir.actions.report,print_report_name:ams_mep.invitation_visitor_report_action
msgid "'Invitation_Badge- %s' % object.invitation_id.name"
msgstr ""

#. module: ams_mep
#: model:mail.template,subject:ams_mep.ams_mep_default_email_template
msgid "({{ object.name }}) - {{ object.invitation_id.subject }}"
msgstr ""

#. module: ams_mep
#: model:mail.template,body_html:ams_mep.ams_mep_default_email_template
msgid ""
"<html>\n"
"                    <body>\n"
"                        <div style=\"font-family: 'Cairo', sans-serif; direction: rtl; background-color: #f3f4f6; color: #1a1a1a; line-height: 1.8; margin: 0; padding: 0;\">\n"
"                            <div style=\"max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);\">\n"
"                                <div style=\"background: linear-gradient(135deg, #004F23 0%, #007A3B 100%); padding: 40px 20px; text-align: center;\">\n"
"                                    <h1 style=\"color: #ffffff; font-size: 28px; font-weight: 700; margin: 0;\">تصريح زيارة</h1>\n"
"                                </div>\n"
"                                <div style=\"padding: 40px 30px;\">\n"
"                                    <h2 style=\"font-size: 24px; font-weight: 600; color: #004F23; margin-bottom: 20px;\">مرحباً <t t-out=\"object.visitor_id.partner_id.name or ''\"></t>،</h2>\n"
"                                    <p style=\"font-size: 16px;\">نرحب بكم في وزارة الاقتصاد والتخطيط. فيما يلي تفاصيل تصريح زيارتكم:</p>\n"
"                                    <div style=\"background-color: #f9fafb; border-radius: 12px; padding: 25px; margin: 30px 0; border: 1px solid #dfe3e8;\">\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">رقم التصريح:</strong> <t t-esc=\"object.name\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">قاعة الاجتماع:</strong> <t t-esc=\"object.invitation_id.room_name\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">الموقع:</strong> <t t-esc=\"object.invitation_id.access_groups_ids[0].name\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">وقت الزيارة:</strong> <t t-esc=\"object.invitation_id._localized_start_date\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">مدة الزيارة:</strong> <t t-esc=\"object.duration_display\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">وقت الخروج:</strong> <t t-esc=\"object.invitation_id._localized_end_date\"></t>\n"
"                                        </div>\n"
"                                    </div>\n"
"                                    <div style=\"background-color: #e7f4ed; padding: 20px; border-left: 6px solid #004F23; border-radius: 8px;\">\n"
"                                        <p style=\"font-size: 16px; margin: 0;\"><strong>ملاحظات هامة:</strong></p>\n"
"                                        <ul style=\"list-style: square inside; padding: 0; margin-top: 10px;\">\n"
"                                            <li style=\"font-size: 14px;\">تم إرفاق تصريح الزيارة مع هذا البريد الإلكتروني.</li>\n"
"                                            <li style=\"font-size: 14px;\">يرجى إبراز هذا التصريح عند نقطة الأمن.</li>\n"
"                                            <li style=\"font-size: 14px;\">يرجى الالتزام بتعليمات الأمن والسلامة.</li>\n"
"                                        </ul>\n"
"                                    </div>\n"
"                                    <p style=\"font-size: 16px; margin-top: 30px;\">نتطلع لاستقبالكم ونتمنى لكم زيارة موفقة.</p>\n"
"                                    <p style=\"font-size: 16px; margin-top: 20px;\">مع أطيب التحيات،<br><strong>إدارة الأمن والسلامة</strong><br>وزارة الاقتصاد والتخطيط</p>\n"
"                                </div>\n"
"                                <div style=\"background-color: #f9fafb; padding: 30px; text-align: center; border-top: 1px solid #dfe3e8;\">\n"
"                                    <a href=\"https://mep.gov.sa/ar\" style=\"color: #004F23; text-decoration: none; font-size: 14px; font-weight: 600; margin: 0 10px;\">موقعنا الإلكتروني</a>\n"
"                                    <a href=\"https://mep.gov.sa/ar/contact-us\" style=\"color: #004F23; text-decoration: none; font-size: 14px; font-weight: 600; margin: 0 10px;\">تواصل معنا</a>\n"
"                                    <p style=\"color: #6c757d; font-size: 12px; margin-top: 20px;\">جميع الحقوق محفوظة لوزارة الاقتصاد والتخطيط ©2024</p>\n"
"                                </div>\n"
"                            </div>\n"
"                        </div>\n"
"                    </body>\n"
"                </html>\n"
"            "
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid ""
"<strong>\n"
"                            <span><i class=\"fa fa-info-circle\" title=\"Info\"/></span>\n"
"                        </strong>\n"
"                        Please fill fields in the form and\n"
"                        <strong>\n"
"                            <i class=\"fa fa-search\" title=\"Search\"/> Search\n"
"                        </strong>\n"
"                        with visitor National or Resident ID if it exist. If it does not exist, click on the\n"
"                        <strong>\n"
"                            <i class=\"fa fa-plus\" title=\"Add\"/> Add\n"
"                        </strong>\n"
"                        button to add a new visitor.\n"
"                        <br/>\n"
"                        in case you want add extra visitors from visitors tab below you can add multiple visitors."
msgstr ""
"<strong>\n"
"    <span><i class=\"fa fa-info-circle\" title=\"Info\"/></span>\n"
"</strong>\n"
"يُرجى ملء الحقول في النموذج ثم "
"<strong><i class=\"fa fa-search\" title=\"Search\"/> البحث</strong> "
"باستخدام رقم هوية الزائر أو المقيم.\n"
"<br/>\n"
"إذا كان الزائر مسجلاً مسبقًا، سيتم استرجاع بياناته تلقائيًا. أما إذا لم يكن مسجلاً، يُرجى النقر على زر "
"<strong><i class=\"fa fa-plus\" title=\"Add\"/> إضافة</strong> "
"لإنشاء سجل جديد للزائر.\n"
"<br/>\n"
"في حال الرغبة في إضافة زوار إضافيين، يمكنك ذلك من خلال تبويب \"الزائرين\" في الأسفل."

#. module: ams_mep
#: model:mail.template,name:ams_mep.ams_mep_default_email_template
msgid "AMS MEP Default Email Template"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Add New Visitor"
msgstr "اضافة زائر جديد"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Confirm Visit"
msgstr "تاكيد الزيارة"

#. module: ams_mep
#: model:ir.model.fields,field_description:ams_mep.field_ams_vm_invitation__extra_visitor_id_number
msgid "Extra Visitor ID"
msgstr "رقم هوية الزائر الإضافي"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid ""
"I acknowledge the accuracy of the attached data. In case of any remarks, "
"please contact the Security Department."
msgstr ""
"اقر بصحة البيانات المرفقة وفى حالة وجود أي ملاحظات يرجى التواصل مع إدارة "
"الأمن."

#. module: ams_mep
#: model:ir.model.fields,field_description:ams_mep.field_ams_vm_invitation__information_confirmed
msgid "I confirm the accuracy of the data"
msgstr ""
"أقر بصحة البيانات المرفقة وفى حالة وجود أي ملاحظات يرجى التواصل مع الأمن"

#. module: ams_mep
#: model:ir.model,name:ams_mep.model_ams_vm_invitation
msgid "Invitation"
msgstr "دعوة"

#. module: ams_mep
#: model:ir.model,name:ams_mep.model_ams_vm_invitation_visitor
msgid "Invitation Visitor Assignment"
msgstr "تعيين الزائر للدعوة"

#. module: ams_mep
#: model:ir.actions.act_window,name:ams_mep.invitations_action
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_view_list_inherit
msgid "Invitations"
msgstr "الدعوات"

#. module: ams_mep
#: model:ir.actions.report,name:ams_mep.invitation_visitor_report_action
msgid "MEP Visitor Invitation Badge"
msgstr ""

#. module: ams_mep
#: model:ir.model.fields,field_description:ams_mep.field_ams_vm_invitation__visitor_id
msgid "Main Visitor"
msgstr "الزائر الرئيسي"

#. module: ams_mep
#: model:ir.model.fields,field_description:ams_mep.field_ams_vm_invitation__visitor_id_number
msgid "Main Visitor ID"
msgstr "رقم هوية الزائر الرئيسي"

#. module: ams_mep
#. odoo-python
#: code:addons/addons_ams/ams_mep/models/invitation.py:0
#: code:addons/addons_ams/ams_mep/models/invitation_visitor.py:0
#: code:addons/ams_mep/models/invitation.py:0
#: code:addons/ams_mep/models/invitation_visitor.py:0
msgid ""
"No visitor found with this ID number, click on add button to add new visitor"
" with this ID number."
msgstr ""
"لا يوجد زائر موجود برقم هوية هذا الزائر، انقر على زر اضافة لاضافة زائر "
"جديد برقم هوية هذا الزائر."

#. module: ams_mep
#. odoo-python
#: code:addons/addons_ams/ams_mep/models/invitation.py:0
#: code:addons/ams_mep/models/invitation.py:0
msgid "Please confirm the accuracy of the data."
msgstr "يرجى الموافقة على إقرار صحة البيانات."

#. module: ams_mep
#. odoo-python
#: code:addons/addons_ams/ams_mep/models/invitation.py:0
#: code:addons/ams_mep/models/invitation.py:0
msgid "Please enter a valid ID number"
msgstr "من فضلك ادخل رقم هوية الزائر بشكل صحيح"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Print Invitation"
msgstr "طباعة الدعوة"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_visitor_report_template
msgid "QR Code"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Search Visitor"
msgstr "بحث عن الزائر"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Send Invitation Email"
msgstr "ارسال الدعوة عبر البريد الألكتروني"

#. module: ams_mep
#: model:ir.model,name:ams_mep.model_ams_vm_visitor
#: model:ir.model.fields,field_description:ams_mep.field_ams_vm_invitation_visitor__visitor_id
msgid "Visitor"
msgstr "الزائر"

#. module: ams_mep
#: model:ir.model.fields,field_description:ams_mep.field_ams_vm_invitation_visitor__visitor_id_number
msgid "Visitor ID"
msgstr "رقم هوية الزائر"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Visitors"
msgstr "الزائرين"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_visitor_report_template
msgid "الاسم :"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_visitor_report_template
msgid "الشركة:"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_visitor_report_template
msgid "المسؤول:"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_visitor_report_template
msgid "الموقع:"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_visitor_report_template
msgid "رقم المرجع:"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_visitor_report_template
msgid "قاعة الاجتماع:"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_visitor_report_template
msgid "وقت الخروج:"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitation_visitor_report_template
msgid "وقت الزيارة:"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Import from Excel"
msgstr "استيراد من Excel"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Import visitors from Excel file"
msgstr "استيراد الزوار من ملف Excel"


#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Host Info"
msgstr "معلومات المضيف"

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Extra Visitors"
msgstr "الزوار الإضافيون"

