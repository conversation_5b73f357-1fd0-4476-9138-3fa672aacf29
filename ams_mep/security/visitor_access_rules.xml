<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Visitor Access Rule: Users can only access visitors they follow -->
        <record id="visitor_follower_access_rule" model="ir.rule">
            <field name="name">My Visitors Access</field>
            <field name="model_id" ref="ams_vm.model_ams_vm_visitor"/>
            <field name="domain_force">[
                '|',
                ('message_follower_ids.partner_id', '=', user.partner_id.id),
                ('create_uid', '=', user.id)
            ]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- Admin users bypass the follower rule -->
        <record id="visitor_admin_access_rule" model="ir.rule">
            <field name="name">Visitors Admin Full Access</field>
            <field name="model_id" ref="ams_vm.model_ams_vm_visitor"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('base.group_system'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- Manager users have broader access -->
        <record id="visitor_manager_access_rule" model="ir.rule">
            <field name="name">Visitors Manager Access</field>
            <field name="model_id" ref="ams_vm.model_ams_vm_visitor"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('ams_base.ams_group_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

    </data>
</odoo>
