# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class ContactLensFittingAttribute(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "emr_frm.contact_lens_fitting"
    _inherit = ['emr_frm.base_form']
    _description = "Contact Lens Fitting Attribute"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic

    # endregion

    # region  Special
    opt_fit_va_sc_od = fields.Char()
    opt_fit_va_sc_os = fields.Char()

    opt_fit_va_cc_od = fields.Char()
    opt_fit_va_cc_os = fields.Char()

    opt_fit_ref_od = fields.Char()
    opt_fit_ref_od_va = fields.Char()
    opt_fit_ref_os = fields.Char()
    opt_fit_ref_os_va = fields.Char()

    opt_fit_sle_lids_lashes_od = fields.Char()
    opt_fit_sle_lids_lashes_os = fields.Char()

    opt_fit_conju_od = fields.Char()
    opt_fit_conju_os = fields.Char()

    opt_fit_cornea_od = fields.Char()
    opt_fit_cornea_os = fields.Char()

    opt_fit_ins_od = fields.Char()
    opt_fit_ins_os = fields.Char()

    opt_fit_lens_od = fields.Char()
    opt_fit_lens_os = fields.Char()

    opt_fit_eye_od_brand = fields.Char()
    opt_fit_eye_od_bc = fields.Char()
    opt_fit_eye_od_power = fields.Char()
    opt_fit_eye_od_diameter = fields.Char()
    opt_fit_eye_od_edge_lift = fields.Char()
    opt_fit_eye_od_color = fields.Char()
    opt_fit_eye_od_iris_purple_size = fields.Char()

    opt_fit_eye_os_brand = fields.Char()
    opt_fit_eye_os_bc = fields.Char()
    opt_fit_eye_os_power = fields.Char()
    opt_fit_eye_os_diameter = fields.Char()
    opt_fit_eye_os_edge_lift = fields.Char()
    opt_fit_eye_os_color = fields.Char()
    opt_fit_eye_os_iris_purple_size = fields.Char()

    opt_fit_od_mov = fields.Char()
    opt_fit_od_coverage = fields.Char()
    opt_fit_od_centration = fields.Char()
    opt_fit_od_rotation = fields.Char()

    opt_fit_os_mov = fields.Char()
    opt_fit_os_coverage = fields.Char()
    opt_fit_os_centration = fields.Char()
    opt_fit_os_rotation = fields.Char()

    opt_fit_over_ref_od = fields.Char()
    opt_fit_over_ref_od_va = fields.Char()
    opt_fit_over_ref_os = fields.Char()
    opt_fit_over_ref_os_va = fields.Char()

    opt_fit_final_rx_od_brand = fields.Char()
    opt_fit_final_rx_od_bc = fields.Char()
    opt_fit_final_rx_od_power = fields.Char()
    opt_fit_final_rx_od_diameter = fields.Char()
    opt_fit_final_rx_od_edge_lift = fields.Char()
    opt_fit_final_rx_od_color = fields.Char()
    opt_fit_final_rx_od_iris_purple_size = fields.Char()

    opt_fit_final_rx_os_brand = fields.Char()
    opt_fit_final_rx_os_bc = fields.Char()
    opt_fit_final_rx_os_power = fields.Char()
    opt_fit_final_rx_os_diameter = fields.Char()
    opt_fit_final_rx_os_edge_lift = fields.Char()
    opt_fit_final_rx_os_color = fields.Char()
    opt_fit_final_rx_os_iris_purple_size = fields.Char()

    opt_fit_final_rx_os_boston = fields.Char()
    opt_fit_final_rx_os_ad_sept = fields.Char()
    opt_fit_final_rx_os_avizor_gp = fields.Char()
    opt_fit_final_rx_os_ever_clean = fields.Char()
    opt_fit_final_rx_os_boston_cleaner = fields.Char()

    # endregion

    # region  Relational
    form_type_id = fields.Many2one('emr_frm.form_type')
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
