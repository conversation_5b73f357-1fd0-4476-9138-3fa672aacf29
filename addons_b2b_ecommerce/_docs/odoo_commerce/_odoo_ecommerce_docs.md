# Odoo E-Commerce Technical Architecture Documentation

## Overview

The Odoo e-commerce module (`website_sale`) provides a comprehensive e-commerce solution that seamlessly integrates the customer-facing online store with the backend ERP system. This document provides a detailed technical analysis of the architecture, focusing on how the e-commerce portal integrates with Odoo's backend systems.

## Module Structure and Dependencies

### Core Dependencies
- **website**: Base website functionality and CMS
- **sale**: Sales order management and core sales logic
- **website_payment**: Payment processing integration
- **website_mail**: Email communication features
- **portal_rating**: Customer rating and review system
- **digest**: Analytics and reporting
- **delivery**: Shipping and delivery management

### Module Architecture Overview

```
website_sale/
├── controllers/          # HTTP controllers for web requests
├── models/              # Data models extending core Odoo models
├── views/               # XML templates and views
├── static/src/          # Frontend assets (JS, CSS, images)
├── security/            # Access control and security rules
├── data/                # Default data and configuration
└── tests/               # Test suites
```

## Technical Architecture

### 1. Frontend-Backend Integration Layer

#### HTTP Controllers (`controllers/`)

The e-commerce system uses multiple specialized controllers to handle different aspects of the shopping experience:

**Main Controller (`main.py`)**
- **WebsiteSale Class**: Inherits from `payment_portal.PaymentPortal`
- **Key Routes**:
  - `/shop` - Product catalog and search
  - `/shop/cart` - Shopping cart management
  - `/shop/checkout` - Checkout process
  - `/shop/payment` - Payment processing
  - `/shop/confirmation` - Order confirmation

**Specialized Controllers**:
- `payment.py` - Payment transaction handling
- `delivery.py` - Shipping method selection
- `variant.py` - Product variant configuration
- `product_configurator.py` - Product customization

#### Session Management

The system maintains shopping cart state through HTTP sessions:
```python
# Cart quantity tracking
request.session['website_sale_cart_quantity'] = sale_order.cart_quantity

# Order tracking
request.session['sale_last_order_id'] = order_sudo.id

# Pricelist caching
request.session['website_sale_current_pl'] = pricelist.id
```

### 2. Data Model Integration

#### Core Model Extensions

**Sale Order (`models/sale_order.py`)**
```python
class SaleOrder(models.Model):
    _inherit = 'sale.order'

    website_id = fields.Many2one('website')  # Links order to website
    cart_recovery_email_sent = fields.Boolean()
    website_order_line = fields.One2many()  # Web-visible order lines
    cart_quantity = fields.Integer()
    is_abandoned_cart = fields.Boolean()
```

**Product Template (`models/product_template.py`)**
```python
class ProductTemplate(models.Model):
    _inherit = ['product.template', 'website.published.multi.mixin']

    website_description = fields.Html()
    public_categ_ids = fields.Many2many('product.public.category')
    website_sequence = fields.Integer()
    alternative_product_ids = fields.Many2many()  # Upselling
    accessory_product_ids = fields.Many2many()   # Cross-selling
```

**Website Configuration (`models/website.py`)**
```python
class Website(models.Model):
    _inherit = 'website'

    # E-commerce specific settings
    shop_ppg = fields.Integer()  # Products per page
    shop_ppr = fields.Integer()  # Products per row
    account_on_checkout = fields.Selection()  # Guest checkout policy
    cart_abandoned_delay = fields.Float()
    ecommerce_access = fields.Selection()  # Access control
```

### 3. Frontend Architecture

#### JavaScript Components (`static/src/js/`)

**Core Website Sale Widget (`website_sale.js`)**
```javascript
export const WebsiteSale = publicWidget.Widget.extend(VariantMixin, cartHandlerMixin, {
    selector: '.oe_website_sale',
    events: {
        'click #add_to_cart': '_onClickAdd',
        'change .js_quantity': '_onChangeCartQuantity',
        'submit .js_add_cart_json': '_onClickAddCartJSON'
    }
});
```

**Key Frontend Components**:
- `cart.js` - Shopping cart functionality
- `checkout.js` - Checkout process handling
- `payment_form.js` - Payment form integration
- `website_sale_configurators.js` - Product configuration
- `website_sale_tracking.js` - Analytics and visitor tracking

#### Template System (`views/templates.xml`)

The frontend uses QWeb templates for rendering:
- **Header Integration**: Cart icon with quantity badge
- **Product Catalog**: Grid/list view with filtering
- **Product Pages**: Detailed product information with variants
- **Cart Pages**: Shopping cart with quantity management
- **Checkout Flow**: Multi-step checkout process

### 4. Security and Access Control

#### Security Rules (`security/ir_rules.xml`)

**Public Product Access**:
```xml
<record id="product_template_public" model="ir.rule">
    <field name="domain_force">[('website_published', '=', True), ('sale_ok', '=', True)]</field>
    <field name="groups" eval="[ref('base.group_public'), ref('base.group_portal')]"/>
</record>
```

#### Access Control Features
- **Guest Checkout**: Configurable guest vs. mandatory login
- **Product Visibility**: Published products only for public users
- **Multi-website**: Company-specific product and pricelist rules
- **Portal Integration**: Customer account management

### 5. Integration Points

#### Backend ERP Integration

**Sales Management**:
- Shopping carts become draft sale orders
- Order confirmation triggers standard sales workflow
- Inventory management through stock moves
- Invoicing integration with accounting module

**Customer Management**:
- Website visitors tracked and converted to partners
- Portal users linked to customer records
- Address management for billing/shipping
- Customer communication through mail system

**Product Management**:
- Products published from backend to website
- Inventory levels reflected in real-time
- Pricing rules through pricelist system
- Product variants and configurators

#### Payment Integration

**Payment Flow**:
1. Cart validation and order creation
2. Payment provider selection
3. Transaction processing through payment module
4. Order confirmation and fulfillment trigger

**Supported Features**:
- Multiple payment providers
- Express checkout (Apple Pay, Google Pay)
- Saved payment methods
- Subscription billing integration

### 6. Performance and Scalability

#### Caching Strategy
- **Product Catalog**: Cached product searches with fuzzy matching
- **Pricelist Computation**: Session-based pricelist caching
- **Static Assets**: CDN-ready asset bundling
- **Database Optimization**: Indexed fields for search performance

#### Session Management
- **Cart Persistence**: Session-based cart storage
- **Visitor Tracking**: Anonymous visitor identification
- **Performance Monitoring**: Built-in analytics and tracking

## E-Commerce to Backend Integration Flow

### Customer Journey Integration

```mermaid
graph TD
    A[Website Visitor] --> B[Product Browsing]
    B --> C[Add to Cart]
    C --> D[Shopping Cart]
    D --> E[Checkout Process]
    E --> F[Payment]
    F --> G[Order Confirmation]
    G --> H[Backend Order Processing]
    H --> I[Inventory Update]
    H --> J[Invoice Generation]
    H --> K[Delivery Processing]

    B --> L[Visitor Tracking]
    L --> M[Analytics Database]

    C --> N[Session Management]
    N --> O[Cart Recovery]

    E --> P[Customer Registration]
    P --> Q[Partner Creation]

    F --> R[Payment Processing]
    R --> S[Transaction Records]
```

### Data Flow Architecture

```mermaid
graph LR
    subgraph "Frontend Layer"
        A[Website Templates]
        B[JavaScript Widgets]
        C[CSS Styling]
    end

    subgraph "Controller Layer"
        D[WebsiteSale Controller]
        E[Payment Controller]
        F[Delivery Controller]
    end

    subgraph "Model Layer"
        G[Sale Order]
        H[Product Template]
        I[Website Visitor]
        J[Payment Transaction]
    end

    subgraph "Backend Integration"
        K[Inventory Management]
        L[Accounting]
        M[CRM]
        N[Delivery]
    end

    A --> D
    B --> D
    D --> G
    E --> J
    F --> N
    G --> K
    G --> L
    I --> M
    H --> K
```

## Key Technical Features

### 1. Product Catalog Management
- **Search and Filtering**: Advanced product search with faceted navigation
- **Category Management**: Hierarchical product categories
- **Product Variants**: Dynamic variant selection and pricing
- **Inventory Integration**: Real-time stock level display

### 2. Shopping Cart System
- **Session-based Storage**: Cart persistence across sessions
- **Dynamic Updates**: AJAX-based cart modifications
- **Abandoned Cart Recovery**: Automated email campaigns
- **Guest Checkout**: Optional anonymous purchasing

### 3. Checkout Process
- **Multi-step Flow**: Address → Delivery → Payment → Confirmation
- **Address Management**: Billing and shipping address handling
- **Delivery Integration**: Real-time shipping calculation
- **Payment Processing**: Secure payment gateway integration

### 4. Customer Portal Integration
- **Account Management**: Customer registration and login
- **Order History**: Past order tracking and reordering
- **Address Book**: Saved addresses for quick checkout
- **Wishlist**: Product favorites and comparison

### 5. Analytics and Tracking
- **Visitor Tracking**: Anonymous and authenticated user tracking
- **Product Analytics**: View counts and popular products
- **Conversion Tracking**: Sales funnel analysis
- **Abandoned Cart Tracking**: Recovery campaign triggers

## Configuration and Customization

### Website Settings
- **Shop Layout**: Grid/list view, products per page
- **Checkout Options**: Guest checkout, mandatory registration
- **Payment Methods**: Enabled payment providers
- **Delivery Options**: Available shipping methods
- **Tax Configuration**: Tax display preferences

### Product Configuration
- **Publishing**: Website publication status
- **Categories**: E-commerce category assignment
- **Variants**: Product option configuration
- **Pricing**: Pricelist and discount rules
- **Images**: Product media management

### Security Configuration
- **Access Control**: Public vs. logged-in user access
- **Data Protection**: GDPR compliance features
- **Payment Security**: PCI DSS compliance
- **Session Security**: Secure session management

## Development Guidelines

### Extending the E-commerce System

**Custom Controllers**:
```python
from odoo.addons.website_sale.controllers.main import WebsiteSale

class CustomWebsiteSale(WebsiteSale):
    @route(['/shop/custom'], type='http', auth="public", website=True)
    def custom_shop_page(self, **kwargs):
        # Custom implementation
        return request.render("custom_template", values)
```

**Model Extensions**:
```python
class ProductTemplate(models.Model):
    _inherit = 'product.template'

    custom_field = fields.Char("Custom Field")

    def _get_custom_data(self):
        # Custom business logic
        return self.custom_field
```

**Frontend Customization**:
```javascript
import { WebsiteSale } from '@website_sale/js/website_sale';

WebsiteSale.include({
    _onCustomEvent: function(ev) {
        // Custom frontend logic
    }
});
```

This architecture provides a robust, scalable e-commerce solution that seamlessly integrates with Odoo's comprehensive ERP system, enabling businesses to manage their entire operation from a single platform.

---

# Custom B2B E-Commerce Technical Solution

## Overview

Based on the analysis of Odoo's e-commerce architecture and the specific requirements for a B2B portal serving KAUST through Zenith Arabia, this section outlines a comprehensive technical solution using **Next.js as the frontend portal** integrated with **Odoo 18 as the backend ERP system**.

## Business Requirements Summary

### Key Stakeholders
- **Zenith Arabia**: Vendor/Supplier managing catalogs, pricing, and fulfillment
- **KAUST**: Customer with multiple departments and purchasing workflows
- **Merchandiser**: Catalog and SKU management
- **Account Manager**: Contract and pricing management
- **KAUST Users**: Purchasing representatives and managers

### Core Features Required
1. **Multi-tenant B2B Portal** with customer-specific branding
2. **Frame Contract Management** with SLAs and compliance monitoring
3. **Customer-specific Pricing** with multiple price lists and dynamic discounts
4. **Quotation/Purchase Order Workflow** with negotiation capabilities
5. **Product Catalog Management** with variants and accessories
6. **Secure Authentication** via Nafath integration
7. **Real-time Order Tracking** and history management

## Proposed Technical Architecture

### System Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer - Next.js"
        A[KAUST Portal /kaust]
        B[Admin Portal /admin]
        C[Vendor Portal /vendor]
        D[Authentication Service]
        E[State Management]
    end

    subgraph "API Gateway"
        F[REST API Gateway]
        G[GraphQL Endpoint]
        H[Authentication Middleware]
        I[Rate Limiting]
    end

    subgraph "Odoo Backend"
        J[Custom B2B Controllers]
        K[Quotation Management]
        L[Contract Management]
        M[Pricing Engine]
        N[Product Catalog]
        O[Customer Portal]
    end

    subgraph "External Services"
        P[Nafath Authentication]
        Q[Email Service]
        R[File Storage]
        S[Analytics]
    end

    A --> F
    B --> F
    C --> F
    D --> H
    F --> J
    G --> J
    J --> K
    J --> L
    J --> M
    J --> N
    H --> P
    K --> Q
    N --> R
    A --> S
```

### Technology Stack

#### Frontend (Next.js 15)
- **Framework**: Next.js 15 with App Router
- **UI Library**: React 18 with TypeScript
- **Styling**: Tailwind CSS with customer theming
- **State Management**: Zustand or Redux Toolkit
- **API Client**: TanStack Query (React Query) + Axios
- **Authentication**: NextAuth.js with custom Nafath provider
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts or Chart.js
- **Testing**: Jest + React Testing Library

#### Backend (Odoo 18)
- **Core**: Odoo Community/Enterprise 18
- **Database**: PostgreSQL 15+
- **Cache**: Redis for session and API caching
- **Queue**: Celery for background tasks
- **File Storage**: MinIO or AWS S3
- **Monitoring**: Prometheus + Grafana

#### Infrastructure
- **Containerization**: Docker + Docker Compose
- **Reverse Proxy**: Nginx with SSL termination
- **CI/CD**: GitHub Actions or GitLab CI
- **Hosting**: Cloud VPS or Kubernetes cluster

## Custom Odoo Modules Architecture

### 1. B2B Base Module (`b2b_base`)

**Purpose**: Core B2B functionality and multi-tenant support

**Key Models**:
```python
class B2BCustomer(models.Model):
    _name = 'b2b.customer'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(required=True)
    code = fields.Char(required=True, index=True)  # e.g., 'kaust'
    partner_id = fields.Many2one('res.partner', required=True)
    portal_theme = fields.Selection([
        ('default', 'Default'),
        ('kaust', 'KAUST Theme'),
        ('custom', 'Custom Theme')
    ])
    logo = fields.Binary()
    primary_color = fields.Char()
    secondary_color = fields.Char()
    active = fields.Boolean(default=True)

class B2BUserProfile(models.Model):
    _name = 'b2b.user.profile'

    user_id = fields.Many2one('res.users', required=True)
    customer_id = fields.Many2one('b2b.customer', required=True)
    role = fields.Selection([
        ('buyer', 'Buyer'),
        ('approver', 'Approver'),
        ('admin', 'Administrator')
    ])
    department = fields.Char()
    approval_limit = fields.Monetary()
```

### 2. Contract Management Module (`b2b_contracts`)

**Purpose**: Frame contract management with SLAs and compliance

**Key Models**:
```python
class B2BContract(models.Model):
    _name = 'b2b.contract'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(required=True)
    customer_id = fields.Many2one('b2b.customer', required=True)
    contract_number = fields.Char(required=True)
    start_date = fields.Date(required=True)
    end_date = fields.Date(required=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('terminated', 'Terminated')
    ], default='draft')

    # SLA Fields
    delivery_sla_days = fields.Integer()
    response_sla_hours = fields.Integer()

    # Terms
    payment_terms = fields.Text()
    delivery_terms = fields.Text()

    # Linked Records
    pricelist_ids = fields.One2many('product.pricelist', 'contract_id')

class B2BContractSLA(models.Model):
    _name = 'b2b.contract.sla'

    contract_id = fields.Many2one('b2b.contract', required=True)
    metric_type = fields.Selection([
        ('delivery_time', 'Delivery Time'),
        ('response_time', 'Response Time'),
        ('quality_score', 'Quality Score')
    ])
    target_value = fields.Float()
    actual_value = fields.Float()
    measurement_date = fields.Date()
    compliance_status = fields.Selection([
        ('met', 'Met'),
        ('missed', 'Missed'),
        ('pending', 'Pending')
    ])
```

### 3. Quotation Management Module (`b2b_quotations`)

**Purpose**: Advanced quotation workflow with negotiation capabilities

**Key Models**:
```python
class B2BQuotation(models.Model):
    _name = 'b2b.quotation'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(required=True)
    customer_id = fields.Many2one('b2b.customer', required=True)
    user_id = fields.Many2one('res.users', required=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted for Review'),
        ('under_review', 'Under Review'),
        ('revised', 'Revised'),
        ('approved', 'Approved'),
        ('converted_to_order', 'Converted to Order'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired'),
        ('on_hold', 'On Hold')
    ], default='draft', tracking=True)

    # Quotation Details
    quotation_date = fields.Datetime(default=fields.Datetime.now)
    validity_date = fields.Date()
    total_amount = fields.Monetary()
    currency_id = fields.Many2one('res.currency')

    # Workflow Fields
    submitted_date = fields.Datetime()
    approved_date = fields.Datetime()
    rejection_reason = fields.Text()

    # Lines
    line_ids = fields.One2many('b2b.quotation.line', 'quotation_id')

    # Negotiation History
    revision_ids = fields.One2many('b2b.quotation.revision', 'quotation_id')

    def action_submit(self):
        self.state = 'submitted'
        self.submitted_date = fields.Datetime.now()
        # Send notification to vendor

    def action_approve(self):
        self.state = 'approved'
        self.approved_date = fields.Datetime.now()

    def action_convert_to_order(self):
        # Create sale order from quotation
        order = self.env['sale.order'].create({
            'partner_id': self.customer_id.partner_id.id,
            'quotation_id': self.id,
            'order_line': [(0, 0, {
                'product_id': line.product_id.id,
                'product_uom_qty': line.quantity,
                'price_unit': line.unit_price,
            }) for line in self.line_ids]
        })
        self.state = 'converted_to_order'
        return order

class B2BQuotationLine(models.Model):
    _name = 'b2b.quotation.line'

    quotation_id = fields.Many2one('b2b.quotation', required=True)
    product_id = fields.Many2one('product.product', required=True)
    quantity = fields.Float(required=True)
    unit_price = fields.Monetary()
    discount_percent = fields.Float()
    subtotal = fields.Monetary(compute='_compute_subtotal')

    @api.depends('quantity', 'unit_price', 'discount_percent')
    def _compute_subtotal(self):
        for line in self:
            line.subtotal = line.quantity * line.unit_price * (1 - line.discount_percent / 100)

class B2BQuotationRevision(models.Model):
    _name = 'b2b.quotation.revision'

    quotation_id = fields.Many2one('b2b.quotation', required=True)
    revision_number = fields.Integer()
    revision_date = fields.Datetime(default=fields.Datetime.now)
    revised_by = fields.Many2one('res.users')
    revision_notes = fields.Text()
    changes_summary = fields.Html()
```

### 4. Advanced Pricing Module (`b2b_pricing`)

**Purpose**: Customer-specific pricing with dynamic rules

**Key Models**:
```python
class ProductPricelist(models.Model):
    _inherit = 'product.pricelist'

    contract_id = fields.Many2one('b2b.contract')
    customer_id = fields.Many2one('b2b.customer')
    is_b2b_pricelist = fields.Boolean(default=False)

class B2BPricingRule(models.Model):
    _name = 'b2b.pricing.rule'

    name = fields.Char(required=True)
    customer_id = fields.Many2one('b2b.customer')
    product_id = fields.Many2one('product.product')
    category_id = fields.Many2one('product.category')

    rule_type = fields.Selection([
        ('fixed_price', 'Fixed Price'),
        ('percentage_discount', 'Percentage Discount'),
        ('volume_discount', 'Volume Discount'),
        ('contract_price', 'Contract Price')
    ])

    # Rule Parameters
    fixed_price = fields.Monetary()
    discount_percentage = fields.Float()
    min_quantity = fields.Float()
    max_quantity = fields.Float()

    # Validity
    date_start = fields.Date()
    date_end = fields.Date()
    active = fields.Boolean(default=True)
```

## API Endpoints Design

### 1. Authentication Endpoints

```python
# controllers/auth.py
class B2BAuthController(http.Controller):

    @http.route('/api/v1/auth/nafath/login', type='json', auth='none', methods=['POST'])
    def nafath_login(self, **kwargs):
        """Authenticate user via Nafath and return JWT token"""
        nafath_token = kwargs.get('nafath_token')
        # Validate with Nafath service
        user_info = self._validate_nafath_token(nafath_token)

        # Create or update user
        user = self._get_or_create_user(user_info)

        # Generate JWT token
        jwt_token = self._generate_jwt_token(user)

        return {
            'success': True,
            'token': jwt_token,
            'user': {
                'id': user.id,
                'name': user.name,
                'email': user.email,
                'customer_code': user.b2b_profile_id.customer_id.code
            }
        }

    @http.route('/api/v1/auth/refresh', type='json', auth='user', methods=['POST'])
    def refresh_token(self):
        """Refresh JWT token"""
        new_token = self._generate_jwt_token(request.env.user)
        return {'token': new_token}
```

### 2. Product Catalog Endpoints

```python
# controllers/catalog.py
class B2BCatalogController(http.Controller):

    @http.route('/api/v1/products', type='json', auth='user', methods=['GET'])
    def get_products(self, **kwargs):
        """Get products with customer-specific pricing"""
        customer = request.env.user.b2b_profile_id.customer_id

        # Apply customer-specific domain
        domain = [
            ('sale_ok', '=', True),
            ('website_published', '=', True)
        ]

        # Add customer-specific filters
        if customer.contract_ids:
            contract_products = customer.contract_ids.mapped('product_ids.id')
            if contract_products:
                domain.append(('id', 'in', contract_products))

        products = request.env['product.product'].search(domain)

        # Get customer-specific pricing
        pricelist = self._get_customer_pricelist(customer)

        result = []
        for product in products:
            price = pricelist._get_product_price(product, 1.0)
            result.append({
                'id': product.id,
                'name': product.name,
                'description': product.description_sale,
                'image_url': f'/web/image/product.product/{product.id}/image_1920',
                'price': price,
                'currency': pricelist.currency_id.name,
                'availability': product.qty_available,
                'variants': self._get_product_variants(product),
                'accessories': self._get_product_accessories(product)
            })

        return {
            'success': True,
            'products': result,
            'total': len(result)
        }

    @http.route('/api/v1/products/<int:product_id>', type='json', auth='user', methods=['GET'])
    def get_product_detail(self, product_id):
        """Get detailed product information"""
        product = request.env['product.product'].browse(product_id)
        customer = request.env.user.b2b_profile_id.customer_id

        if not product.exists():
            return {'success': False, 'error': 'Product not found'}

        # Get pricing tiers for volume discounts
        pricing_tiers = self._get_volume_pricing(product, customer)

        return {
            'success': True,
            'product': {
                'id': product.id,
                'name': product.name,
                'description': product.description_sale,
                'specifications': product.website_description,
                'images': self._get_product_images(product),
                'pricing_tiers': pricing_tiers,
                'stock_info': {
                    'available': product.qty_available,
                    'forecasted': product.virtual_available,
                    'lead_time': product.sale_delay
                },
                'documents': self._get_product_documents(product)
            }
        }
```

### 3. Quotation Management Endpoints

```python
# controllers/quotations.py
class B2BQuotationController(http.Controller):

    @http.route('/api/v1/quotations', type='json', auth='user', methods=['GET'])
    def get_quotations(self, **kwargs):
        """Get user's quotations with filtering"""
        customer = request.env.user.b2b_profile_id.customer_id

        domain = [('customer_id', '=', customer.id)]

        # Add filters
        if kwargs.get('state'):
            domain.append(('state', '=', kwargs['state']))

        quotations = request.env['b2b.quotation'].search(domain, order='create_date desc')

        return {
            'success': True,
            'quotations': [{
                'id': q.id,
                'name': q.name,
                'state': q.state,
                'total_amount': q.total_amount,
                'currency': q.currency_id.name,
                'quotation_date': q.quotation_date.isoformat(),
                'validity_date': q.validity_date.isoformat() if q.validity_date else None,
                'line_count': len(q.line_ids)
            } for q in quotations]
        }

    @http.route('/api/v1/quotations', type='json', auth='user', methods=['POST'])
    def create_quotation(self, **kwargs):
        """Create new quotation"""
        customer = request.env.user.b2b_profile_id.customer_id

        quotation = request.env['b2b.quotation'].create({
            'name': kwargs.get('name', 'New Quotation'),
            'customer_id': customer.id,
            'user_id': request.env.user.id,
            'validity_date': kwargs.get('validity_date'),
            'line_ids': [(0, 0, {
                'product_id': line['product_id'],
                'quantity': line['quantity'],
                'unit_price': line['unit_price']
            }) for line in kwargs.get('lines', [])]
        })

        return {
            'success': True,
            'quotation_id': quotation.id,
            'message': 'Quotation created successfully'
        }

    @http.route('/api/v1/quotations/<int:quotation_id>/submit', type='json', auth='user', methods=['POST'])
    def submit_quotation(self, quotation_id):
        """Submit quotation for review"""
        quotation = request.env['b2b.quotation'].browse(quotation_id)

        if not quotation.exists():
            return {'success': False, 'error': 'Quotation not found'}

        if quotation.state != 'draft':
            return {'success': False, 'error': 'Quotation cannot be submitted'}

        quotation.action_submit()

        return {
            'success': True,
            'message': 'Quotation submitted for review'
        }

    @http.route('/api/v1/quotations/<int:quotation_id>/convert', type='json', auth='user', methods=['POST'])
    def convert_to_order(self, quotation_id):
        """Convert approved quotation to order"""
        quotation = request.env['b2b.quotation'].browse(quotation_id)

        if quotation.state != 'approved':
            return {'success': False, 'error': 'Only approved quotations can be converted'}

        order = quotation.action_convert_to_order()

        return {
            'success': True,
            'order_id': order.id,
            'message': 'Quotation converted to order successfully'
        }
```