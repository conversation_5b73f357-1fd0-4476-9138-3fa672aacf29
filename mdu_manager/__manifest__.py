# -*- coding: utf-8 -*-

{
    "name": "mdu_manager",
    "version": "1.0.0",
    "depends": [
        'mdu_base'
    ],
    "author": "laplacesoftware",
    "category": "MDU",
    "website": "https://www.laplacesoftware.com/",
    "images": ["static/description/images/main_screenshot.jpg"],
    "price": "0",
    "license": "OPL-1",
    "currency": "USD",
    "summary": "MDU module to manage sites and network devices",
    "description": """ 
    MDU typically refers to the management system used in Multi-Dwelling Unit (MDU) networks.
     MDUs are residential buildings or complexes with multiple separate housing units,
      such as apartment buildings, condominiums, or gated communities.
       Managing networks in such environments requires specialized software or systems
        to ensure efficient delivery of services like internet, cable television, and telephone to each unit.
    - Module Manage (Controller, Domains,Zones,Profile,WLANs) 
    - Site Manager
        Manage site, building, floor, units, vlan, dpsks

     """,
    "data": [
        "security/security.xml",
        "security/ir.model.access.csv",
        "data/sequences.xml",

        "views/sz_controller_api_version.xml",
        "views/sz_controller_views.xml",
        "views/sz_domain_views.xml",
        "views/sz_zone_views.xml",

        "views/nt_data_plan_views.xml",
        "views/nt_device_model_views.xml",
        "views/nt_brand_views.xml",
        "views/nt_port_views.xml",
        "views/nt_router_views.xml",
        "views/nt_switch_views.xml",
        "views/nt_vlan_views.xml",
        "views/nt_wlan_views.xml",
        "views/nt_wlan_ssid_views.xml",
        "views/nt_ppsk_profile_views.xml",

        "views/site_unit_views.xml",
        "views/site_floor_views.xml",
        "views/site_building_views.xml",
        "views/site_views.xml",

        "views/scenario_views.xml",

        "views/contract_views.xml",

        "views/partner_views.xml",
        "views/site_units_subscription_views.xml",




        "views/menu.xml"
    ],
    "installable": True,
    "auto_install": False,
    "application": True,
}
