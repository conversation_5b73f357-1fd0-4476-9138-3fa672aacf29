from odoo import models, fields, api
from odoo.api import depends
from odoo.exceptions import UserError


class PPSKProfile(models.Model):
    _name = "mdu.ppsk_profile"
    _inherit = ["mdu_base.abstract_model", "mdu_base.api_base_model"]

    # region Fields ----------------------------------------------------------

    # endregion

    # region Relation Fields --------------------------------------------------
    ssids_ids = fields.Many2many("mdu.wlan_ssid", string="SSIDs")
    units_ids = fields.One2many("mdu.unit", "profile_id", string="PPSKs")
    allow_delete_active_dpsk = fields.Boolean()

    site_id = fields.Many2one("mdu.site", compute="_compute_site_id", string="Site", store=True)
    zone_id = fields.Many2one("mdu.zone", string="Zone", required=True)
    domain_id = fields.Many2one(related="zone_id.domain_id", store=True)
    controller_id = fields.Many2one(related="zone_id.controller_id", store=True)

    # endregion

    # region Compute Methods --------------------------------------------------
    @api.depends('zone_id')
    def _compute_site_id(self):
        """Compute the related site_id based on zone_id."""
        for record in self:
            record.site_id = self.zone_id.site if self.zone_id else False

    # endregion

    # region ---------------------- Action Methods ---------------------------------------
    def action_push_profile(self):
        """
        Push PPSK profiles to the Controller MDU .

        This function makes a call to the  MDU API to retrieve the list of dpsks or success result
        and updates the records with the received information.

        Parameters:
        - self: the current object instance

        Returns:
        No return value
        """
        self.site_id.validate_site_zone()
        self.reset_error()
        zone_id = self.zone_id.record_id
        wlan_id = self.zone_id.tenant_wlan_id.record_id
        ppsk_data = self.api_client.prepare_push_dpsk_data(profile=self)
        # send record_id in case of update
        api_kwargs = self.site_id.get_api_params(profile_id=self.record_id)
        context_kwargs = self.site_id.get_context_params(profile_id=self.id)
        response = self.api_client.push_dpsk(self.api_base_url, zone_id, wlan_id, self.api_token, ppsk_data,
                                             **api_kwargs)
        if self.is_success_response(response):
            dpsk_info_list = self.get_response_list(response)
            if len(dpsk_info_list) > 1:  # dpsk_info_list (Ruckus API)
                for dpsk_vals in dpsk_info_list:
                    synced_unit = self.env["mdu.unit"].with_context(**context_kwargs).create_or_update(dpsk_vals)
            else:
                self.units_ids.handle_sync_success()
                self.handle_api_push_response(response)  # (Omada API)

                # print(synced_unit.unit_id, synced_unit.dpsk_id)
        else:
            self.assign_error(response, raise_error=True)

    def action_sync_profile(self):
        """TODO implement sync profiles"""
        ...
        self.validate_controller_api()
        self.reset_error()  # Reset any previous error states

        if not self.zone_id:
            raise UserError("Zone | site must be selected")

        api_kwargs = self.site_id.get_api_params()

        # Fetch site data from the API
        response = self.api_client.get_profile_by_id(
            base_url=self.api_base_url,
            zone_id=self.zone_id.record_id,
            profile_id=self.record_id,
            token=self.api_token,
            **api_kwargs
        )

        # Handle the API response
        self.handle_api_sync_response(response)

    def action_sync_site_profiles(self):
        """
        Sync PPSK profiles from the Controller MDU .

        This function makes a call to the  MDU API to retrieve the list of wlans
        and updates the local database with the received information.

        Parameters:
        - self: the current object instance

        Returns:
        No return value
        """
        self.reset_error()
        response = {}
        if not self.zone_id:
            raise UserError("Zone | site must be selected")

        api_kwargs = self.site_id.get_api_params()
        context_kwargs = self.site_id.get_context_params()

        response = self.api_client.get_ppsk_profiles(self.api_base_url, self.record_id, self.api_token, **api_kwargs)
        if self.is_success_response(response):
            for vals in self.get_response_list(response):
                profile = self.env["mdu.ppsk_profile"].with_context(**context_kwargs).create_or_update(vals)
                if profile:
                    profile._sync_ppsks()

        else:
            self.assign_error(response)

        # response = self.api_client.get_wlan_by_id(self.api_base_url, self.zone_id.record_id, self.record_id, self.api_token)
        # self.handle_api_sync_response(response)

    # endregion

    # region ---------------------- Business Methods -------------------------------------
    def create_or_update(self, profile_vals):
        """
        Create or update a wlan based on the provided wlan values.

        :param profile_vals: A dictionary containing the values for the wlan
        :type profile_vals: dict
        :return: The updated or newly created wlan record
        :rtype: mdu.ppsk_profile Recordset
        """
        zone_id = self.get_zone_id(profile_vals, self.zone_id.id)

        controller_id = self._context.get("controller_id") or self.controller_id.id

        profile_vals.update({"zone_id": zone_id, "controller_id": controller_id})
        # TODO : handle ssids_ids m2m relation
        prepare_vals = self.api_client.prepare_ppsk_profile_vals(profile_vals)
        record = super().create_or_update(prepare_vals)

        return record

    def _sync_ppsks(self):
        """
        Sync PPSks from the Controller MDU .

        This function makes a call to the  MDU API to retrieve the list of ppsks
        and updates the local database with the received information.

        Parameters:
        - self: the current object instance

        Returns:
        No return value
        """
        self.reset_error()
        response = {}
        api_kwargs = self.site_id.get_api_params()
        context_kwargs = self.site_id.get_context_params(profile_id=self.id)

        response = self.api_client.get_ppsks(self.api_base_url, self.record_id, self.api_token, **api_kwargs)
        if self.is_success_response(response):
            self.logger.info("profile>>self.api_client.get_ppsks")
            for vals in self.get_response_list(response):
                ppsk_record = self.env["mdu.unit"].with_context(**context_kwargs).create_or_update(vals)
                self.logger.info(f"profile>>self.api_client.create_or_update:{ppsk_record.name}")
        else:
            self.assign_error(response)

            # endregion

    def prepare_ppsks_data(self):
        return self.api_client.prepare_push_dpsk_data(profile=self)

    # endregion
