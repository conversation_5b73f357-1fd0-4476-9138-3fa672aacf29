from odoo import models, fields, api


class Domain(models.Model):
    _name = "mdu.domain"
    _inherit = ["mdu_base.abstract_model", "mdu_base.api_base_model"]

    # region ---------------------- Fields Declaration ----------------------------------
    domain_type = fields.Char(string="Domain Type")
    # endregion

    # region ---------------------- Relational Fields -----------------------------------

    controller_id = fields.Many2one("mdu.zone_controller", string="Controller", required=True, ondelete="restrict")
    zones_ids = fields.One2many("mdu.zone", "domain_id", string="Zones")

    parent_domain_id = fields.Many2one("mdu.domain", string="Parent Domain")
    domain_ids = fields.One2many("mdu.domain", "parent_domain_id", string="Sub Domains")

    # endregion

    # region ---------------------- Business Methods -------------------------------------

    # endregion

    # region ---------------------- Action Methods ---------------------------------------

    def action_sync_domain(self):
        """
        Syncs the domain with the API by retrieving the domain information and creating or updating it locally.
        """
        self.reset_error()
        response = self.api_client.get_domain_by_id(self.api_base_url, self.record_id, self.api_token)
        self.handle_api_sync_response(response)

    def action_push_domain(self):
        """
        Perform a synchronous action to create the zone using the provided API client.
        """
        self.reset_error()

        domain_vals = {
            "name": self.name
        }
        # "domainType": "PARTNER"

        record_id = self.record_id if self.synced else False
        parent_domain_id = self.parent_domain_id.record_id if self.parent_domain_id else False

        response = self.api_client.push_domain(self.api_base_url, domain_vals, self.api_token, record_id, parent_domain_id)
        self.handle_api_push_response(response)

    def action_sync_zones(self):
        """
        Sync zones from the Ruckus MDU API.

        This function makes a call to the Ruckus MDU API to retrieve the list of zones
        and updates the local database with the received information.

        Parameters:
        - self: the current object instance

        Returns:
        No return value
        """
        self.reset_error()
        all_zones = self._context.get("all_zones")
        response = {}
        if all_zones:
            response = self.api_client.get_zones(self.api_base_url, self.api_token)
        else:
            response = self.api_client.get_zones(self.api_base_url, self.api_token, self.record_id)

        if self.is_success_response(response):
            for zone_vals in self.get_response_list(response):
                self.env["mdu.zone"].with_context(domain_id=self.id).create_or_update(zone_vals)
        else:
            self.assign_error(response)

    def create_or_update(self, domain_vals):
        """
        Create or update a domain record based on the provided domain values.

        :param domain_vals: dictionary containing the values for the domain record
        :type domain_vals: dict
        :return: mdu.domain Recordset
        """
        controller_id = self._context.get("controller_id") or (self.controller_id.id if self.controller_id else False)
        prepare_vals = self.api_client.prepare_domain_vals(domain_vals)
        prepare_vals.update({"controller_id": controller_id})
        record = super().create_or_update(prepare_vals)
        return record
    # endregion
