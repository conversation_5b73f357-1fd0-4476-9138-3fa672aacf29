from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import requests
import json


class ResConfig(models.TransientModel):
    _inherit = "res.config.settings"

    ruckus_api_url = fields.Char(string="API URL", store=True, config_parameter="mdu_ruckus.api_url")
    ruckus_username = fields.Char(string="Username", store=True, config_parameter="mdu_ruckus.username")
    ruckus_password = fields.Char(string="Password", store=True, config_parameter="mdu_ruckus.password")
    ruckus_token = fields.Char(string="Token", store=True, config_parameter="mdu_ruckus.token", help="Service Ticket")


