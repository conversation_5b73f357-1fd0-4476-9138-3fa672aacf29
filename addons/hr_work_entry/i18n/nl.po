# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_employee.py:0
msgid "%s work entries"
msgstr "%s werkboekingen"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_employee_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Work Entries\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Werkboekingen\n"
"                            </span>"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "<span>Hours</span>"
msgstr "<span>Uren</span>"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__active
msgid "Active"
msgstr "Actief"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: hr_work_entry
#: model:hr.work.entry.type,name:hr_work_entry.work_entry_type_attendance
msgid "Attendance"
msgstr "Aanwezigheid"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "Beginning"
msgstr "Begin"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__cancelled
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry__code
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__code
msgid ""
"Careful, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"Let op, de Code wordt in veel verwijzingen gebruikt, het wijzigen ervan kan "
"leiden tot ongewenste wijzigingen."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__color
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__color
msgid "Color"
msgstr "Kleur"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__conflict
msgid "Conflict"
msgstr "Conflict"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Conflicting"
msgstr "Conflicterend"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__conflict
msgid "Conflicts"
msgstr "Conflicten"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__country_id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__country_id
msgid "Country"
msgstr "Land"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__country_code
msgid "Country Code"
msgstr "Landcode"

#. module: hr_work_entry
#: model_terms:ir.actions.act_window,help:hr_work_entry.hr_work_entry_type_action
msgid "Create a new work entry type"
msgstr "Maak een nieuwe soort werkboeking"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Current Month"
msgstr "Huidige maand"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Date"
msgstr "Datum"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__department_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Department"
msgstr "Afdeling"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Description"
msgstr "Omschrijving"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__draft
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Draft"
msgstr "Concept"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__duration
msgid "Duration"
msgstr "Duur"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__employee_id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__employee_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Employee"
msgstr "Werknemer"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "End"
msgstr "Einde"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__external_code
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__external_code
msgid "External Code"
msgstr "Externe code"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_start
msgid "From"
msgstr "Van"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR-werkboeking"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR werkboekingstype"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_employee__has_work_entries
msgid "Has Work Entries"
msgstr "Heeft werkboekingen"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__id
msgid "ID"
msgstr "ID"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the work "
"entry type without removing it."
msgstr ""
"Als het actief veld is uitgezet kun je de werkboeking verbergen zonder het "
"te verwijderen."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__user_id
msgid "Me"
msgstr "Ik"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__name
msgid "Name"
msgstr "Naam"

#. module: hr_work_entry
#: model_terms:ir.actions.act_window,help:hr_work_entry.hr_work_entry_action
msgid "No data to display"
msgstr "Geen gegevens om te tonen"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Note: Validated work entries cannot be modified."
msgstr "Opmerking: Bevestigde werkboekingen kunnen niet worden gewijzigd."

#. module: hr_work_entry
#: model:hr.work.entry.type,name:hr_work_entry.overtime_work_entry_type
msgid "Overtime Hours"
msgstr "Overuren"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__code
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__code
msgid "Payroll Code"
msgstr "Loonadministratie code"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Resource verlof detail"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Search Work Entry"
msgstr "Zoek werkboeking"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
msgid "Search Work Entry Type"
msgstr "Zoek werkboekingstype"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: hr_work_entry
#. odoo-javascript
#: code:addons/hr_work_entry/static/src/xml/work_entry_templates.xml:0
msgid "Solve conflicts first"
msgstr "Los eerst conflicten op"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Start Date"
msgstr "Begindatum"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_start_before_end
msgid "Starting time should be before end time."
msgstr "Starttijd moet voor eindtijd liggen."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__state
msgid "State"
msgstr "Status"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"De ISO-landcode in twee letters.\n"
"Je kunt dit veld gebruiken voor snelzoeken."

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "The same code cannot be associated to multiple work entry types."
msgstr ""
"Dezelfde code kan niet worden gekoppeld aan meerdere werkboekingstypes."

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "This work entry is validated. You can't delete it."
msgstr "Deze werkboeking is gevalideerd en kan niet verwijderd worden."

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Time Off Options"
msgstr "Verlof opties"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_stop
msgid "To"
msgstr "T/m"

#. module: hr_work_entry
#: model_terms:ir.actions.act_window,help:hr_work_entry.hr_work_entry_action
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""
"Probeer enkele records toe te voegen of zorg ervoor dat er geen actieve "
"filter in de zoekbalk staat."

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Type"
msgstr "Soort"

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "Undefined"
msgstr "Niet gedefinieerd"

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "Undefined Type"
msgstr "Ongedefinieerd Type"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry__external_code
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__external_code
msgid "Use this code to export your data to a third party"
msgstr ""
"Gebruik deze code om je gegevens naar een externe partij te exporteren"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__validated
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Validated"
msgstr "Bevestigd"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entries_no_validated_conflict
msgid "Validated work entries cannot overlap"
msgstr "Bevestigde werkboekingen mogen elkaar niet overlappen"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Werkdetail"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_pivot
msgid "Work Entries"
msgstr "Werkboekingen"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_user_work_entry_employee
msgid "Work Entries Employees"
msgstr "Werkboekingen werknemers"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action_conflict
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry"
msgstr "Werkboeking"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry Name"
msgstr "Naam werkboeking"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_attendance__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_leaves__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.resource_calendar_leaves_view_search_inherit
msgid "Work Entry Type"
msgstr "Werkboekingstype"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Work Entry Type Name"
msgstr "Naam werkboekingstype"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_type_action
msgid "Work Entry Types"
msgstr "Werkboekingstypes"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_has_end
msgid "Work entry must end. Please define an end date or a duration."
msgstr "Een werkboeking moet eindigen. Definieer een einddatum of een duur."

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid ""
"You can't change the Country of this work entry type cause it's currently "
"used by the system. You need to delete related working entries first."
msgstr ""
"Je kunt het land van dit werkinvoertype niet wijzigen omdat het momenteel "
"door het systeem wordt gebruikt. Je moet eerst gerelateerde werkinvoeren "
"verwijderen."

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "You can't change the country of this specific work entry type."
msgstr "Je kunt het land van dit specifieke werkinvoertype niet wijzigen."

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_user_work_entry_employee_user_id_employee_id_unique
msgid "You cannot have the same employee twice."
msgstr "Je kunt niet twee keer dezelfde werknemer hebben."
