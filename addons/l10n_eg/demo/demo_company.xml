<?xml version="1.0" encoding="utf-8"?>
<odoo>    <data>
        <record id="base.partner_demo_company_eg" model="res.partner" forcecreate="1">
            <field name="name">EG Company</field>
            <field name="country_id" ref="base.eg"/>
            <field name="state_id" ref="base.state_eg_c"/>
            <field name="street">112 26th July St.</field>
            <field name="city">Zamalek</field>
            <field name="vat">*********</field>
            <field name="phone">+20 27 370 423</field>
            <field name="email"><EMAIL></field>
            <field name="website">www.egexample.com</field>
        </record>

        <record id="base.demo_company_eg" model="res.company" forcecreate="1">
            <field name="name">EG Company</field>
            <field name="partner_id" ref="base.partner_demo_company_eg"/>
        </record>

        <function model="res.company" name="_onchange_country_id">
            <value eval="[ref('base.demo_company_eg')]"/>
        </function>

        <function model="res.users" name="write">
            <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
            <value eval="{'company_ids': [Command.link(ref('base.demo_company_eg'))]}"/>
        </function>

        <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>eg</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_eg')"/>
        <value name="install_demo" eval="True"/>
    </function>
    </data>
</odoo>
