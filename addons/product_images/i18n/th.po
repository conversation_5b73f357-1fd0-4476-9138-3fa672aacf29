# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_images
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"%(matching_images_count)s matching images have been found for "
"%(product_count)s products."
msgstr ""
"พบรูปภาพที่ตรงกัน %(matching_images_count)s สำหรับสินค้า %(product_count)s"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"<span invisible=\"nb_products_selected &lt;= 10000\">\n"
"                            As only 10,000 products can be processed per day, the remaining will be\n"
"                            done tomorrow.\n"
"                        </span>"
msgstr ""
"<span invisible=\"nb_products_selected &lt;= 10000\">\n"
"                            เนื่องจากสามารถดำเนินการสินค้าได้เพียง 10,000 รายการต่อวัน ส่วนที่เหลือจะ\n"
"                            ดำเนินการในวันพรุ่งนี้\n"
"                        </span>"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"A task to process products in the background is already running. Please try "
"againlater."
msgstr ""
"งานเพื่อดำเนินการผลิตภัณฑ์ในเบื้องหลังกำลังทำงานอยู่แล้ว "
"กรุณาลองใหม่อีกครั้งในภายหลัง"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.res_config_settings_view_form
msgid "API Key"
msgstr "API Key"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: product_images
#: model:ir.model,name:product_images.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: product_images
#: model:ir.model,name:product_images.model_product_fetch_image_wizard
msgid ""
"Fetch product images from Google Images based on the product's barcode "
"number."
msgstr "ดึงภาพผลิตภัณฑ์จาก Google Images ตามหมายเลขบาร์โค้ดของผลิตภัณฑ์"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "Get Pictures"
msgstr "รับรูปภาพ"

#. module: product_images
#: model:ir.actions.act_window,name:product_images.product_product_action_get_pic_with_barcode
#: model:ir.actions.act_window,name:product_images.product_template_action_get_pic_with_barcode
msgid "Get Pictures from Google Images"
msgstr "รับรูปภาพจาก Google Images"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_res_config_settings__google_custom_search_key
msgid "Google Custom Search API Key"
msgstr "คีย์ API การค้นหาที่กำหนดเองของ Google"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__id
msgid "ID"
msgstr "ไอดี"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_product__image_fetch_pending
msgid "Image Fetch Pending"
msgstr "รอการดึงข้อมูลรูปภาพ"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_unable_to_process
msgid "Number of product unprocessable"
msgstr "จำนวนผลิตภัณฑ์ที่ไม่สามารถดำเนินการได้"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_to_process
msgid "Number of products to process"
msgstr "จำนวนผลิตภัณฑ์ที่จะดำเนินการ"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_selected
msgid "Number of selected products"
msgstr "จำนวนผลิตภัณฑ์ที่เลือก"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"Please note that some images might not be royalty-free. You should not\n"
"                        publish these on your website."
msgstr ""
"โปรดทราบว่ารูปภาพบางรูปอาจไม่ไม่มีค่าลิขสิทธิ์ คุณไม่ควร\n"
"                       เผยแพร่สิ่งเหล่านี้บนเว็บไซต์ของคุณ"

#. module: product_images
#: model:ir.actions.server,name:product_images.ir_cron_fetch_image_ir_actions_server
msgid "Product Images: Get product images from Google"
msgstr "รูปภาพผลิตภัณฑ์: รับรูปภาพผลิตภัณฑ์จาก Google"

#. module: product_images
#: model:ir.model,name:product_images.model_product_product
msgid "Product Variant"
msgstr "ตัวเลือกย่อยสินค้า"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid "Product images"
msgstr "รูปภาพสินค้า"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__products_to_process
msgid "Products To Process"
msgstr "ผลิตภัณฑ์ที่ต้องดำเนินการ"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"Products are processed in the background. Images will be updated "
"progressively."
msgstr ""
"สินค้าได้รับการประมวลผลในเบื้องหลังแล้ว "
"รูปภาพจะได้รับการอัปเดตอย่างต่อเนื่อง"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.res_config_settings_view_form
msgid "Search Engine ID"
msgstr "Search Engine ID"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid "The API Key and Search Engine ID must be set in the General Settings."
msgstr "ต้องตั้งค่าคีย์ API และ Search Engine ID ในการตั้งค่าทั่วไป"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"The Custom Search API is not enabled in your Google project. Please visit "
"your Google Cloud Platform project page and enable it, then retry. If you "
"enabled this API recently, please wait a few minutes and retry."
msgstr ""
"Custom Search API ไม่ได้เปิดใช้งานในโปรเจ็กต์ Google ของคุณ "
"โปรดไปที่หน้าโปรเจ็กต์ Google Cloud Platform ของคุณแล้วเปิดใช้งาน "
"จากนั้นลองอีกครั้ง หากคุณเปิดใช้งาน API นี้แล้ว โปรดรอสักครู่และลองอีกครั้ง"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_res_config_settings__google_pse_id
msgid "The identifier of the Google Programmable Search Engine"
msgstr "ตัวระบุของ Google Programmable Search Engine"

#. module: product_images
#: model:ir.model.fields,help:product_images.field_product_fetch_image_wizard__products_to_process
msgid ""
"The list of selected products that meet the criteria (have a barcode and no "
"image)"
msgstr "รายการสินค้าที่ถูกคัดเลือกเข้าเกณฑ์ (มีบาร์โค้ด และไม่มีรูปภาพ)"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"The scheduled action \"Product Images: Get product images from Google\" has "
"been deleted. Please contact your administrator to have the action restored "
"or to reinstall the module \"product_images\"."
msgstr ""
"การดำเนินการตามกำหนดเวลา \"รูปภาพผลิตภัณฑ์: รับรูปภาพผลิตภัณฑ์จาก Google\" "
"ได้ถูกลบแล้ว "
"โปรดติดต่อผู้ดูแลระบบของคุณเพื่อเรียกคืนการดำเนินการหรือติดตั้งโมดูล "
"\"product_images\" ใหม่"

#. module: product_images
#. odoo-python
#: code:addons/product_images/models/ir_cron_trigger.py:0
msgid "This action is already scheduled. Please try again later."
msgstr "การดำเนินการนี้ถูกกำหนดไว้แล้ว กรุณาลองใหม่อีกครั้งในภายหลัง"

#. module: product_images
#: model:ir.model,name:product_images.model_ir_cron_trigger
msgid "Triggered actions"
msgstr "การกระทำที่ทริกเกอร์"

#. module: product_images
#: model:ir.model.fields,help:product_images.field_product_product__image_fetch_pending
msgid "Whether an image must be fetched for this product. Handled by a cron."
msgstr "ต้องดึงรูปภาพสำหรับผลิตภัณฑ์นี้หรือไม่ จัดการโดย cron"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "You selected"
msgstr "คุณเลือกแล้ว"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid "Your API Key or your Search Engine ID is incorrect."
msgstr "รหัส API หรือ Search Engine ID ของคุณไม่ถูกต้อง"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "of which will be processed."
msgstr "ซึ่งจะถูกดำเนินการ"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"products will not be\n"
"                            processed because they either already have an image or their barcode\n"
"                            number is not set."
msgstr ""
"สินค้าจะไม่ได้รับ\n"
"                             การดำเนินการเนื่องจากมีรูปภาพอยู่แล้วหรือไม่ได้ตั้งค่า\n"
"                             หมายเลขบาร์โค้ด"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "products,"
msgstr "สินค้า,"
