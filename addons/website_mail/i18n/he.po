# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_mail
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "<small>Follow</small><i class=\"fa fa-fw ms-1\"/>"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "<small>Unfollow</small><i class=\"fa fa-fw ms-1\"/>"
msgstr ""

#. module: website_mail
#. odoo-javascript
#: code:addons/website_mail/static/src/js/follow.js:0
msgid "Error"
msgstr "שגיאה"

#. module: website_mail
#: model:ir.model,name:website_mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "הסכם אחריות מפרסם"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Subscribe"
msgstr "הירשם כמנוי"

#. module: website_mail
#. odoo-python
#: code:addons/website_mail/controllers/main.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "פעילות חשודה זוהתה על ידי Google reCaptcha."

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Unsubscribe"
msgstr "בטל את המנוי"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "your email..."
msgstr "הדוא\"ל שלך..."
