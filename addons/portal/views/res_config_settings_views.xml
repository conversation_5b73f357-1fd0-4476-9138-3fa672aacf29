<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.portal</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="40"/>
            <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name=%(base.action_apikeys_admin)d]//ancestor::setting" position="inside">
                    <div groups="base.group_no_one">
                        <div class="mt16 content-group">
                            <field name="portal_allow_api_keys"/>
                            <label string="Customers can generate API Keys" for="portal_allow_api_keys"/>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
