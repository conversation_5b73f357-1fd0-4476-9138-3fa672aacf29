# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_it_stock_ddt
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-22 15:53+0000\n"
"PO-Revision-Date: 2022-01-11 14:02+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: \n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_it_stock_ddt
#. odoo-python
#: code:addons/l10n_it_stock_ddt/models/stock_picking.py:0
msgid "%(warehouse)s Sequence %(code)s"
msgstr "%(warehouse)s Sequenza %(code)s"

#. module: l10n_it_stock_ddt
#: model:ir.actions.report,print_report_name:l10n_it_stock_ddt.action_report_ddt
msgid ""
"'DDT - %s - %s' % (object.partner_id.name or '', object.l10n_it_ddt_number)"
msgstr ""

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<b>Note:</b>"
msgstr "<b>Nota:</b>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<b>Total:</b>"
msgstr "<b>Totale:</b>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<b>Transportation Method Details: </b>"
msgstr "<b>Dettagli metodo di trasporto</b>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Carrier Signature</strong>"
msgstr "<strong>Firma corriere</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Company Signature</strong>"
msgstr "<strong>Firma azienda</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Customer Address</strong>"
msgstr "<strong>Indirizzo cliente</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Customer Signature</strong>"
msgstr "<strong>Firma cliente</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Disc.%</strong>"
msgstr "<strong>Sconto%</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Product</strong>"
msgstr "<strong>Prodotto</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Quantity</strong>"
msgstr "<strong>Quantità</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Total Value</strong>"
msgstr "<strong>Valore totale</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Warehouse Address</strong>"
msgstr "<strong>Indirizzo magazzino</strong>"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__attemped_sale
msgid "Attempted Sale"
msgstr "Tentata vendita"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Carrier"
msgstr "Corriere"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Carrier Condition"
msgstr "Termini di resa"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_method__courier
msgid "Courier service"
msgstr "Servizio corriere"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.view_picking_form_inherit_l10n_it_ddt
msgid "DDT Information"
msgstr "Informazioni DDT"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_ddt_number
msgid "DDT Number"
msgstr "Numero DDT"

#. module: l10n_it_stock_ddt
#: model:ir.actions.report,name:l10n_it_stock_ddt.action_report_ddt
msgid "DDT report"
msgstr "DDT"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.account_invoice_view_form_inherit_ddt
msgid "DDTs"
msgstr "DDT"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Documento di Trasporto"
msgstr "Documento di trasporto"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__evaluation
msgid "Evaluation"
msgstr "Conto visione"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__gift
msgid "Gift"
msgstr "Omaggio"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Gross Weight (kg)"
msgstr "Peso lordo (kg)"

#. module: l10n_it_stock_ddt
#: model:ir.model,name:l10n_it_stock_ddt.model_account_move
msgid "Journal Entry"
msgstr "Registrazione contabile"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_bank_statement_line__l10n_it_ddt_ids
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_move__l10n_it_ddt_ids
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_payment__l10n_it_ddt_ids
msgid "L10N It Ddt"
msgstr "L10N It DDT"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_bank_statement_line__l10n_it_ddt_count
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_move__l10n_it_ddt_count
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_payment__l10n_it_ddt_count
msgid "L10N It Ddt Count"
msgstr "Numero L10N It DDT"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking_type__l10n_it_ddt_sequence_id
msgid "L10N It Ddt Sequence"
msgstr "Sequenza L10N It DDT"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_show_print_ddt_button
msgid "L10N It Show Print Ddt Button"
msgstr "Mostra pulsante Stampa DDT L10N It"

#. module: l10n_it_stock_ddt
#. odoo-python
#: code:addons/l10n_it_stock_ddt/models/account_invoice.py:0
msgid "Linked deliveries"
msgstr "Spedizioni associate"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__loaned_use
msgid "Loaned for Use"
msgstr "Prestito d'uso"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Order"
msgstr "Ordine"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__outsourcing
msgid "Outsourcing"
msgstr "Conto lavorazione"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_parcels
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Parcels"
msgstr "Colli"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Picking Number"
msgstr "Numero prelievo"

#. module: l10n_it_stock_ddt
#: model:ir.model,name:l10n_it_stock_ddt.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipologia prelievo"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.view_picking_form_inherit_l10n_it_ddt
msgid "Print"
msgstr "Stampa"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_method__recipient
msgid "Recipient"
msgstr "Destinatario"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__repair
msgid "Repair"
msgstr "Conto riparazione"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__substitution
msgid "Returned goods"
msgstr "Reso"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__sale
msgid "Sale"
msgstr "Vendita"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_method__sender
msgid "Sender"
msgstr "Mittente"

#. module: l10n_it_stock_ddt
#. odoo-python
#: code:addons/l10n_it_stock_ddt/models/stock_picking.py:0
msgid "Sequence %(code)s"
msgstr "Sequenza %(code)s"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Shipping Date"
msgstr "Data trasporto"

#. module: l10n_it_stock_ddt
#: model:ir.model,name:l10n_it_stock_ddt.model_stock_picking
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__transfer
msgid "Transfer"
msgstr "Trasferimento"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_transport_method
msgid "Transport Method"
msgstr "Metodo di trasporto"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_transport_method_details
msgid "Transport Note"
msgstr "Nota di trasporto"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_transport_reason
msgid "Transport Reason"
msgstr "Ragione trasporto"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Transportation Method"
msgstr "Metodo di trasporto"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Transportation Reason"
msgstr "Ragione trasporto"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "VAT:"
msgstr "Part. IVA:"
