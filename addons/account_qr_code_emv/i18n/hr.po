# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_qr_code_emv
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "A bank account is required for EMV QR Code generation."
msgstr ""

#. module: account_qr_code_emv
#: model:ir.model,name:account_qr_code_emv.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankovni računi"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__display_qr_setting
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__display_qr_setting
msgid "Display Qr Setting"
msgstr ""

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "EMV Merchant-Presented QR-code"
msgstr ""

#. module: account_qr_code_emv
#: model_terms:ir.ui.view,arch_db:account_qr_code_emv.view_partner_bank_form_inherit_account
msgid "EMV QR Configuration"
msgstr ""

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__include_reference
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__include_reference
msgid "Include Reference"
msgstr ""

#. module: account_qr_code_emv
#: model:ir.model.fields,help:account_qr_code_emv.field_account_setup_bank_manual_config__include_reference
#: model:ir.model.fields,help:account_qr_code_emv.field_res_partner_bank__include_reference
msgid "Include the reference in the QR code."
msgstr ""

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "Missing Merchant Account Information."
msgstr ""

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "Missing Merchant City."
msgstr ""

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "Missing Proxy Type."
msgstr ""

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "Missing Proxy Value."
msgstr ""

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid ""
"No EMV QR Code is available for the country of the account "
"%(account_number)s."
msgstr ""

#. module: account_qr_code_emv
#: model:ir.model.fields.selection,name:account_qr_code_emv.selection__res_partner_bank__proxy_type__none
msgid "None"
msgstr "Ništa"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__proxy_type
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__proxy_type
msgid "Proxy Type"
msgstr ""

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__proxy_value
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__proxy_value
msgid "Proxy Value"
msgstr ""
