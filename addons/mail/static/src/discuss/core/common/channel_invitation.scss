.o-discuss-ChannelInvitation {
    min-height: 0;
}

.o-discuss-ChannelInvitation-selectable {
    &.o-odd {
        background-color: $gray-100 !important;;
    }
    &:hover {
        background-color: mix($gray-100, $gray-200) !important;
    }
    &.o-selected {
        background-color: mix($o-view-background-color, $o-action, 85%) !important;;
    }
}

.o-discuss-ChannelInvitation-selectedList {
    max-height: 100px;

    button {
        background-color: mix($o-view-background-color, $o-action, 85%);

        &:not(:hover) .oi-close {
            border-color: transparent !important;
        }

        &:hover .oi-close {
            color: $danger;
            border-color: $danger !important;
        }
    }
}

.o-discuss-ChannelInvitation-avatar {
    width: 32px;
    aspect-ratio: 1;
}

.o-discuss-ChannelInvitation-invitationBox {
    position: sticky;
    bottom: - map-get($spacers, 2); // Ignore p-2 class of ActionPanel
}
