<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

<t t-name="mail.ChatHub">
    <div class="o-mail-ChatHub" t-if="isShown or ui.isSmall">
        <t t-if="!store.chatHub.compact">
            <t t-foreach="chatHub.opened" t-as="cw" t-key="cw.localId">
                <ChatWindow chatWindow="cw" right="env.embedLivechat ? chatHub.WINDOW_GAP : (chatHub.BUBBLE_START + chatHub.BUBBLE + (chatHub.BUBBLE_OUTER*2) + (chatHub.opened.length - cw_index - 1) * ((chatHub.isBig ? chatHub.WINDOW_LARGE : chatHub.WINDOW) + chatHub.WINDOW_INBETWEEN * 2))"/>
            </t>
        </t>
        <div t-if="isShown" class="o-mail-ChatHub-bubbles position-fixed end-0 d-flex flex-column align-content-start justify-content-end align-items-center" t-att-class="{ 'bottom-0': !store.chatHub.compact or compactPosition.top === 'auto' }" t-ref="bubbles" t-att-style="store.chatHub.compact ? `top: ${ compactPosition.top }; left: ${ compactPosition.left };` : ''">
            <div class="d-flex flex-column align-content-start justify-content-end align-items-center gap-1">
                <t t-if="store.chatHub.compact">
                    <t t-call="mail.ChatHub.compactButton"/>
                </t>
                <t t-else="">
                    <Dropdown t-if="(chatHub.opened.length + chatHub.folded.length) gt 0" state="options" position="'top-end'" menuClass="'o-mail-ChatHub-optionsMenu o-mail-ChatHub-menu d-flex flex-column bg-view shadow-sm m-0 p-0 mb-1'">
                        <button class="o-mail-ChatHub-bubbleBtn btn o-mail-ChatHub-optionsBtn fa fa-ellipsis-h bg-view mt-1" t-att-class="{ 'opacity-0': !bubblesHover.isHover and !options.isOpen, 'o-active': bubblesHover.isHover or options.isOpen }" title="Chat Options"/>
                        <t t-set-slot="content">
                            <button class="o-mail-ChatHub-option btn border-0 d-flex align-items-center rounded-0 fw-normal" t-on-click="() => this.toggleChatSize()"><div class="form-check form-switch m-0 smaller" style="min-height: auto;"><input class="form-check-input" type="checkbox" role="switch" t-att-checked="this.chatHub.isBig ? 'checked' : ''" t-att-title="chatSizeTransitionText"/></div> <span>Large windows</span></button>
                            <button class="o-mail-ChatHub-option btn border-0 d-flex align-items-center gap-2 rounded-0 fw-normal" t-on-click="() => chatHub.compact = true"><i class="fa fa-fw fa-eye-slash ms-1"></i>Hide all conversations</button>
                            <button class="o-mail-ChatHub-option btn border-0 d-flex align-items-center gap-2 rounded-0 fw-normal" t-on-click="() => chatHub.closeAll()"><i class="oi fa-fw oi-close ms-1"></i>Close all conversations</button>
                        </t>
                    </Dropdown>
                    <t t-foreach="chatHub.folded.slice(0, chatHub.maxFolded)" t-as="cw" t-key="cw.localId">
                        <ChatBubble chatWindow="cw"/>
                    </t>
                    <t t-if="chatHub.folded.length > chatHub.maxFolded" t-call="mail.ChatHub.hiddenButton"/>
                </t>
            </div>
        </div>
    </div>
</t>

<t t-name="mail.ChatHub.compactButton">
    <!-- In dropdown to keep same layout as other items-->
    <Dropdown manual="true">
        <div class="o-mail-ChatHub-bubbleBtn o-mail-ChatHub-compact o-mail-ChatBubble justify-content-center" t-ref="compact">
            <div t-if="compactCounter > 0" class="o-mail-ChatHub-hiddenBtnCounter position-absolute badge rounded-pill fw-bold o-discuss-badge shadow">
                <t t-out="compactCounter"/>
            </div>
            <button class="o-mail-ChatHub-bubbleBtn btn fs-2" t-on-click="expand">
                <i class="o-mail-ChatHub-hiddenBtnIcon d-flex justify-content-center align-items-center btn rounded-circle shadow-sm fa fa-commenting"/>
            </button>
        </div>
    </Dropdown>
</t>

<t t-name="mail.ChatHub.hiddenButton">
    <Dropdown t-if="chatHub.folded.length > chatHub.maxFolded" state="more" position="'left-middle'" menuClass="'o-mail-ChatHub-hiddenMenu o-mail-ChatHub-menu bg-view shadow-sm p-0 m-0'" manual="true">
        <div class="o-mail-ChatBubble o-mail-ChatHub-hiddenBtn justify-content-center" t-att-class="{ 'o-active': more.isOpen }" t-on-click="() => store.chatHub.compact = true" t-ref="more-button">
            <div t-if="hiddenCounter > 0" class="o-mail-ChatHub-hiddenBtnCounter position-absolute badge rounded-pill fw-bold o-discuss-badge shadow">
                <t t-out="hiddenCounter"/>
            </div>
            <button class="o-mail-ChatHub-bubbleBtn btn outline-0">
                <span class="o-mail-ChatHub-hiddenBtnIcon d-flex justify-content-center align-items-center btn rounded-circle shadow-sm fs-2" t-att-class="{ 'o-active': more.isOpen }">+<t t-esc="chatHub.folded.slice(chatHub.maxFolded).length"/></span>
            </button>
        </div>
        <t t-set-slot="content">
            <ul class="m-0 p-0 overflow-auto" role="menu" t-ref="more-menu">
                <t t-foreach="chatHub.folded.slice(chatHub.maxFolded)" t-as="cw" t-key="cw.localId">
                    <li class="o-mail-ChatHub-hiddenItem gap-2 px-2 py-1" role="menuitem" t-att-name="cw.displayName" t-on-click="() => cw.open()">
                        <img class="o-mail-ChatHub-hiddenAvatar rounded-circle o_object_fit_cover" t-att-src="cw.thread?.avatarUrl" alt="Thread image" draggable="false"/>
                        <p class="text-truncate fw-bold mb-0" t-esc="cw.displayName"/>
                        <div t-if="cw.thread?.importantCounter > 0" class="o-mail-ChatHub-hiddenCounter badge rounded-pill fw-bold o-discuss-badge" style="padding: 3px 6px">
                            <t t-out="cw.thread?.importantCounter"/>
                        </div>
                        <button class="o-mail-ChatHub-hiddenClose o-mail-ChatBubble-close d-flex align-items-center rounded" t-on-click.stop="() => cw.close()">
                            <i class="oi oi-close"/>
                        </button>
                    </li>
                </t>
            </ul>
        </t>
    </Dropdown>
</t>

</templates>
