# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid ""
"<select id=\"o_authorize_account_type\" required=\"\" class=\"form-select\">\n"
"                        <option value=\"checking\">Personal Checking</option>\n"
"                        <option value=\"savings\">Personal Savings</option>\n"
"                        <option value=\"businessChecking\">Business Checking</option>\n"
"                    </select>"
msgstr ""
"<select id=\"o_authorize_account_type\" required=\"\" class=\"form-select\">\n"
"                        <option value=\"checking\">Compte-chèques personnel</option>\n"
"                        <option value=\"savings\">Compte d'épargne personnel</option>\n"
"                        <option value=\"businessChecking\">Compte-chèques professionnel</option>\n"
"                    </select>"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "ABA Routing Number"
msgstr "Numéro de routage ABA"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_client_key
msgid "API Client Key"
msgstr "Clé Client API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_login
msgid "API Login ID"
msgstr "Identifiant API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_signature_key
msgid "API Signature Key"
msgstr "Clé de Signature API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_transaction_key
msgid "API Transaction Key"
msgstr "Clé de transaction API"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Account Number"
msgstr "Numéro de compte"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_provider__code__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.Net Profile ID"
msgstr "ID de profil Authorize.Net"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Account Type"
msgstr "Type de compte bancaire"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Name"
msgstr "Nom de la banque"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Code"
msgstr "Code de la carte"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Number"
msgstr "Numéro de carte"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__code
msgid "Code"
msgstr "Code"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid ""
"Could not fetch merchant details:\n"
"%s"
msgstr ""
"Impossible de récupérer les détails du commerçant :\n"
"%s"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid ""
"Could not retrieve the transaction details. (error code: %(error_code)s; "
"error_details: %(error_message)s)"
msgstr ""
"Impossible de récupérer les détails de la transaction. (code d'erreur : "
"%(error_code)s; error_details : %(error_message)s)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Expiration"
msgstr "Expiration"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid ""
"Failed to authenticate.\n"
"%s"
msgstr ""
"Échec de l'authentification.\n"
"%s"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "Generate Client Key"
msgstr "Générer Clé Client"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "How to get paid with Authorize.Net"
msgstr "Comment se faire payer avec Authorize.Net"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Name On Account"
msgstr "Nom sur le compte"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Aucune transaction ne correspond à la référence %s."

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid "Only one currency can be selected by Authorize.Net account."
msgstr "Une seule devise peut être sélectionnée par le compte Authorize.Net."

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_provider
msgid "Payment Provider"
msgstr "Fournisseur de paiement"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "Jeton de paiement"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transaction"

#. module: payment_authorize
#. odoo-javascript
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "Échec du traitement du paiement"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid "Received data with status code \"%(status)s\" and error code \"%(error)s\""
msgstr ""
"Données reçues avec le code de statut \"%(status)s\" et le code d'erreur "
"\"%(error)s\""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/controllers/main.py:0
msgid "Received tampered payment request data."
msgstr "Réception de données de demande de paiement falsifiées."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "Set Account Currency"
msgstr "Définir la devise du compte"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__authorize_login
msgid "The ID solely used to identify the account with Authorize.Net"
msgstr ""
"L'identifiant uniquement utilisé pour identifier le compte avec "
"Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__authorize_client_key
msgid ""
"The public client key. To generate directly from Odoo or from Authorize.Net "
"backend."
msgstr ""
"La clé publique du client. À générer directement depuis Odoo ou depuis le "
"backend d'Authorize.Net."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Le code technique de ce fournisseur de paiement."

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid ""
"The transaction is not in a status to be refunded. (status: %(status)s, "
"details: %(message)s)"
msgstr ""
"Le statut de la transaction ne permet pas de la rembourser. (statut "
":%(status)s, détails : %(message)s)"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "La transaction n'est pas liée à un jeton."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"The unique reference for the partner/token combination in the Authorize.net "
"backend."
msgstr ""
"La référence unique pour la combinaison partenaire/jeton dans le backend "
"d'Authorize.net."

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid "This action cannot be performed while the provider is disabled."
msgstr ""
"Impossible d'exécuter cette action quand le fournisseur est désactivé."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "YY"
msgstr "AA"
