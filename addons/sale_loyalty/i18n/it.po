# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_loyalty
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid " - On products with the following taxes: %(taxes)s"
msgstr "- Su prodotti con le seguenti imposte: %(taxes)s"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_purchased_gift_card
msgid "<span class=\"fa fa-clipboard\"/> Copy"
msgstr "<span class=\"fa fa-clipboard\"/> Copia"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_order_portal_loyalty_content
msgid "<strong>Loyalty Card</strong>"
msgstr "<strong>Carta fedeltà</strong>"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "A better global discount is already applied."
msgstr "Viene già applicato uno sconto globale migliore."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid ""
"A minimum of %(amount)s %(currency)s should be purchased to get the reward"
msgstr ""
"Per ottenere il riconoscimento devono essere effettuati acquisti per un "
"minimo di %(amount)s %(currency)s"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_loyalty_coupon_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_loyalty_reward_wizard_view_form
msgid "Apply"
msgstr "Applica"

#. module: sale_loyalty
#: model:ir.actions.act_window,name:sale_loyalty.sale_loyalty_reward_wizard_action
msgid "Available Rewards"
msgstr "Ricompense disponibili"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_loyalty_reward_wizard_view_form
msgid "Choose a product:"
msgstr "Scegli un prodotto:"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_loyalty_reward_wizard_view_form
msgid "Choose your reward:"
msgstr "Scegli la tua ricompensa:"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.used_gift_card
msgid "Code:"
msgstr "Codice:"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_coupon_points__coupon_id
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_line__coupon_id
msgid "Coupon"
msgstr "Buono sconto"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_coupon_wizard__coupon_code
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_order_view_form_inherit_sale_loyalty
msgid "Coupon Code"
msgstr "Codice buono sconto"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order__coupon_point_ids
msgid "Coupon Point"
msgstr "Punti buono sconto"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/wizard/sale_loyalty_reward_wizard.py:0
msgid "Coupon not found while trying to add the following reward: %s"
msgstr ""
"Non è stato possibile trovare il buono sconto mentre si provava ad "
"aggiungere la seguente ricompensa: %s"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_loyalty_reward_wizard_view_form
msgid "Coupons & Loyalty"
msgstr "Buoni sconto & Fedeltà"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_coupon_wizard__create_uid
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__create_uid
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_coupon_points__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_coupon_wizard__create_date
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__create_date
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_coupon_points__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_loyalty_coupon_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_loyalty_reward_wizard_view_form
msgid "Discard"
msgstr "Abbandona"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "Discount %(desc)s%(tax_str)s"
msgstr "Sconto %(desc)s%(tax_str)s"

#. module: sale_loyalty
#: model:ir.ui.menu,name:sale_loyalty.menu_discount_loyalty_type_config
msgid "Discount & Loyalty"
msgstr "Sconti e fedeltà"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_coupon_wizard__display_name
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__display_name
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_coupon_points__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: sale_loyalty
#: model:ir.actions.act_window,name:sale_loyalty.sale_loyalty_coupon_wizard_action
msgid "Enter Promotion or Coupon Code"
msgstr "Inserire codice promozione o buono sconto"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.used_gift_card
msgid "Expired Date:"
msgstr "Data di scadenza:"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "Free Product - %(product)s"
msgstr "Prodotto gratuito-%(product)s"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_purchased_gift_card
msgid "Gift #"
msgstr "Regalo #"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_purchased_gift_card
msgid "Gift Card Code"
msgstr "Codice carta regalo"

#. module: sale_loyalty
#: model:ir.ui.menu,name:sale_loyalty.menu_gift_ewallet_type_config
msgid "Gift cards & eWallet"
msgstr "Carte regalo e portafoglio elettronico"

#. module: sale_loyalty
#: model:ir.model,name:sale_loyalty.model_loyalty_history
msgid "History for Loyalty cards and Ewallets"
msgstr "Cronologia carte fedeltà e portafogli elettronici"

#. module: sale_loyalty
#: model:ir.model.fields,help:sale_loyalty.field_sale_order_line__points_cost
msgid "How much point this reward costs on the loyalty card."
msgstr "Valore in punti della ricompensa nella carta fedeltà."

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_coupon_wizard__id
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__id
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_coupon_points__id
msgid "ID"
msgstr "ID"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "Invalid product to claim."
msgstr "Prodotto non valido per la richiesta."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/wizard/sale_loyalty_coupon_wizard.py:0
msgid "Invalid sales order."
msgstr "Ordine di vendita non valido."

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_line__is_reward_line
msgid "Is a program reward line"
msgstr "È una riga riconoscimento del programma"

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_order_portal_loyalty_content
msgid "Issued"
msgstr "Emesso"

#. module: sale_loyalty
#. odoo-javascript
#: code:addons/sale_loyalty/static/src/views/fields/loyalty_total/loyalty_data_field.xml:0
msgid "Issued :"
msgstr "Emesso:"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_coupon_wizard__write_uid
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__write_uid
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_coupon_points__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_coupon_wizard__write_date
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__write_date
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_coupon_points__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: sale_loyalty
#. odoo-javascript
#: code:addons/sale_loyalty/static/src/views/fields/loyalty_total/loyalty_data_field.xml:0
msgid "Loyalty Card :"
msgstr "Carta fedeltà:"

#. module: sale_loyalty
#: model:ir.model,name:sale_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "Buono sconto fedeltà"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order__loyalty_data
msgid "Loyalty Data"
msgstr "Dati fedeltà"

#. module: sale_loyalty
#: model:ir.model,name:sale_loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "Programma fedeltà"

#. module: sale_loyalty
#: model:ir.model,name:sale_loyalty.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "Premio fedeltà"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order__applied_coupon_ids
msgid "Manually Applied Coupons"
msgstr "Buoni sconto applicati manualmente"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order__code_enabled_rule_ids
msgid "Manually Triggered Rules"
msgstr "Regole attivate manualmente"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__multi_product_reward
msgid "Multi Product"
msgstr "Multi-Prodotto"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid ""
"No card found for this loyalty program and no points will be given with this"
" order."
msgstr ""
"Non è stata trovata nessuna carta per il seguente programma fedeltà. Non "
"saranno attribuiti punti all'ordine."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "No product is compatible with this promotion."
msgstr "Nessun prodotto è compatibile con la promozione."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/wizard/sale_loyalty_reward_wizard.py:0
msgid "No reward selected."
msgstr "Nessuna ricompensa selezionata."

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_loyalty_reward_wizard_view_form
msgid "No rewards available for this customer!"
msgstr "Nessuna ricompensa disponibile!"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "One or more rewards on the sale order is invalid. Please check them."
msgstr ""
"Una o più ricompense dell'ordine di vendita non sono valide. Per favore, "
"effettua una verifica."

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_coupon_wizard__order_id
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__order_id
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_coupon_points__order_id
msgid "Order"
msgstr "Ordina"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "Order %s"
msgstr "Ordine %s"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_loyalty_program__order_count
msgid "Order Count"
msgstr "Numero ordini"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_loyalty_card__order_id
msgid "Order Reference"
msgstr "Riferimento ordine"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_coupon_points__points
msgid "Points"
msgstr "Punti"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_line__points_cost
msgid "Points Cost"
msgstr "Costo punti"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__reward_ids
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_line__reward_id
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_order_view_form_inherit_sale_loyalty
msgid "Reward"
msgstr "Premio"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order__reward_amount
msgid "Reward Amount"
msgstr "Importo riconoscimento"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_order_line__reward_identifier_code
msgid "Reward Identifier Code"
msgstr "Codice identificativo ricompensa"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__reward_product_ids
msgid "Reward Products"
msgstr "Prodotti premio"

#. module: sale_loyalty
#: model:ir.model,name:sale_loyalty.model_sale_loyalty_coupon_wizard
msgid "Sale Loyalty - Apply Coupon Wizard"
msgstr "Fedeltà vendita-procedura guidata utilizzo buono sconto"

#. module: sale_loyalty
#: model:ir.model,name:sale_loyalty.model_sale_loyalty_reward_wizard
msgid "Sale Loyalty - Reward Selection Wizard"
msgstr "Fedeltà vendita-procedura guidata selezione ricompensa"

#. module: sale_loyalty
#: model:ir.model,name:sale_loyalty.model_sale_order_coupon_points
msgid ""
"Sale Order Coupon Points - Keeps track of how a sale order impacts a coupon"
msgstr ""
"Punti buono sconto ordine di vendita-Tieni traccia dell'impatto di un ordine"
" di vendita su un buono sconto"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_loyalty_program__sale_ok
msgid "Sales"
msgstr "Vendite"

#. module: sale_loyalty
#: model:ir.model,name:sale_loyalty.model_sale_order
msgid "Sales Order"
msgstr "Ordine di vendita"

#. module: sale_loyalty
#: model:ir.model,name:sale_loyalty.model_sale_order_line
msgid "Sales Order Line"
msgstr "Riga ordine di vendita"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__selected_product_id
msgid "Selected Product"
msgstr "Prodotto selezionata"

#. module: sale_loyalty
#: model:ir.model.fields,field_description:sale_loyalty.field_sale_loyalty_reward_wizard__selected_reward_id
msgid "Selected Reward"
msgstr "Ricompensa selezionata"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "TEMPORARY DISCOUNT LINE"
msgstr "RIGA SCONTO TEMPORANEO"

#. module: sale_loyalty
#: model:ir.model.fields,help:sale_loyalty.field_sale_order_line__reward_identifier_code
msgid ""
"Technical field used to link multiple reward lines from the same reward "
"together."
msgstr ""
"Campo tecnico utilizzato per collegare più righe ricompensa della stessa "
"insieme."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "The coupon can only be claimed on future orders."
msgstr ""
"Il buono sconto può essere utilizzato esclusivamente su ordini successivi."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "The coupon does not have enough points for the selected reward."
msgstr ""
"Il buono sconto non presenta punti a sufficenza per la ricompensa "
"selezionata."

#. module: sale_loyalty
#: model:ir.model.constraint,message:sale_loyalty.constraint_sale_order_coupon_points_order_coupon_unique
msgid "The coupon points entry already exists."
msgstr "La voce relativa ai punti del buono sconto esiste già."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "The program is not available for this order."
msgstr "Il programma non è disponibile per quest'ordine."

#. module: sale_loyalty
#: model:ir.model.fields,help:sale_loyalty.field_loyalty_card__order_id
msgid "The sales order from which coupon is generated"
msgstr "Ordine di vendita da cui viene generato il buono sconto"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "There is nothing to discount"
msgstr "Non c'è nulla da scontare"

#. module: sale_loyalty
#: model:ir.model.fields,help:sale_loyalty.field_sale_loyalty_reward_wizard__reward_product_ids
msgid "These are the products that can be claimed with this rule."
msgstr ""
"Questi sono i prodotti che possono essere richiesti con questa regola."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "This code is expired (%s)."
msgstr "Il codice è scaduto (%s)."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "This code is invalid (%s)."
msgstr "Il codice non è valido (%s)."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "This coupon has already been used."
msgstr "Il buono sconto è già stato utilizzato."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "This coupon is expired."
msgstr "Il buono sconto è scaduto."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid ""
"This discount (%(discount)s) is not compatible with \"%(other_discount)s\". "
"Please remove it in order to apply this one."
msgstr ""
"Lo sconto (%(discount)s) non è compatibile con \"%(other_discount)s\". "
"Eliminalo per applicare questo."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "This program cannot be applied with code."
msgstr "Il programma non può essere applicato con il codice."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "This program is already applied to this order."
msgstr "Il programma è stato già applicato all'ordine."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "This program is not available for public users."
msgstr "Il programma non è disponibile per utenti pubblici."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "This program requires a code to be applied."
msgstr "Il programma richiede un codice da applicare."

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "This promo code is already applied."
msgstr "Il codice promozionale è già stato applicato."

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_order_view_form_inherit_sale_loyalty
msgid "Update current promotional lines and select new rewards if applicable."
msgstr ""
"Aggiorna le promozioni attuali e seleziona nuovi premi se applicabile."

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_order_portal_loyalty_content
msgid "Used"
msgstr "Utilizzato"

#. module: sale_loyalty
#. odoo-javascript
#: code:addons/sale_loyalty/static/src/views/fields/loyalty_total/loyalty_data_field.xml:0
msgid "Used :"
msgstr "Usato:"

#. module: sale_loyalty
#. odoo-python
#: code:addons/sale_loyalty/models/sale_order.py:0
msgid "You don't have the required product quantities on your sales order."
msgstr ""
"Nell'ordine di vendita non sono presenti le quantità di prodotto richieste."

#. module: sale_loyalty
#: model_terms:ir.ui.view,arch_db:sale_loyalty.sale_purchased_gift_card
msgid ""
"You will find below your gift cards code. An email has been sent with it. "
"You can use it starting right now."
msgstr ""
"Troverai qui sotto il tuo codice di carta regalo. Un'e-mail è stata inviata "
"con esso. Puoi usarlo a partire da adesso."
