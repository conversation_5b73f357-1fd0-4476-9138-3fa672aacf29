# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 12:50+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid ""
"\"\n"
"                (ID:"
msgstr ""
"\"\n"
"                (ID :"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
msgid "\"%s\" tag is added"
msgstr "étiquette \"%s\" est ajoutée"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"\"On live update\" automation rules can only be used with \"Execute Python "
"Code\" action type."
msgstr ""
"\"Lors de la mise à jour directe\" Les règles d'automatisation ne peuvent "
"être utilisées qu'avec le type d'action \"Exécuter un code Python\"."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "%s actions"
msgstr "%s actions"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "1 action"
msgstr "1 action"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<code>env</code>: environment on which the action is triggered"
msgstr "<code>env</code>: environnement dans lequel l'action est déclenchée"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>model</code>: model of the record on which the action is triggered; is"
" a void recordset"
msgstr ""
"<code>model</code>: modèle de l'enregistrement sur lequel l'action est "
"déclenché : est un jeu d'enregistrements void"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>payload</code>: the payload of the call (GET parameters, JSON body), "
"as a dict."
msgstr ""
"<code>payload</code> : la charge utile de l'appel (paramètres GET, corps "
"JSON), sous forme de dict."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>time</code>, <code>datetime</code>, <code>dateutil</code>, "
"<code>timezone</code>: useful Python libraries"
msgstr ""
"<code>time</code>, <code>datetime</code>, <code>dateutil</code>, "
"<code>timezone</code> : bibliothèques Python utiles"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"Actions\"/>"
msgstr "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"Actions\"/>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-info-circle\"/> The default target record getter will work "
"out-of-the-box for any webhook coming from another Odoo instance."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Le getter d'enregistrement cible par défaut"
" fonctionne immédiatement pour tout webhook provenant d'une autre instance "
"d'Odoo."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-warning\"/> Automation rules triggered by UI changes will "
"be executed <em>every time</em> the watched fields change, <em>whether you "
"save or not</em>."
msgstr ""
"<i class=\"fa fa-warning\"/> Les règles d'automatisation déclenchées par "
"l'interface utilisateur seront exécutées <em>chaque fois</em> que les champs"
" surveillés changent, <em>que vous enregistriez ou non</em>."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span class=\"text-muted\"> Available variables: </span>"
msgstr "<span class=\"text-muted\"> Variables disponibles : </span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"evaluation_type != 'value'\">to</span>\n"
"                                                        <span invisible=\"evaluation_type != 'equation'\">as</span>"
msgstr ""
"<span invisible=\"evaluation_type != 'value'\">à</span>\n"
"                                                        <span invisible=\"evaluation_type != 'equation'\">comme</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"trigger != 'on_time_created'\">after creation</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">after last update</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">after</span>"
msgstr ""
"<span invisible=\"trigger != 'on_time_created'\">après la création</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">après la dernière mise à jour</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">après</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by adding</span>"
msgstr "<span>en ajoutant</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by clearing it</span>"
msgstr "<span>en l'effaçant</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by removing</span>"
msgstr "<span>en supprimant</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by setting it to</span>"
msgstr "<span>en le définissant sur</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<strong><i class=\"fa fa-lock\"/> Keep it secret, keep it safe.</strong>"
msgstr ""
"<strong><i class=\"fa fa-lock\"/> Gardez-le secret, gardez-le en "
"sécurité.</strong>"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__name
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__name
msgid "Action Name"
msgstr "Nom de l'action"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_ids
msgid "Actions"
msgstr "Actions"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Actions To Do"
msgstr "Actions à effectuer"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "Actif"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Add an action"
msgstr "Ajouter une action"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Add followers"
msgstr "Ajouter des abonnés"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Add followers: %(partner_names)s"
msgstr "Ajouter des abonnés : %(partner_names)s"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_created
msgid "After creation"
msgstr "Après la création"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_updated
msgid "After last update"
msgstr "Après la dernière mise à jour"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "Appliquer sur"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "Archivé"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Automate <em>everything</em> with Automation Rules"
msgstr "Automatisez <em>tout</em> grâce aux règles d'automatisation"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__base_automation_id
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__base_automation_id
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Automation Rule"
msgstr "Règle d'automatisation"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Automation Rule Name"
msgstr "Nom de la règle d'automatisation"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation Rules"
msgstr "Règles d'automatisation"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
msgid "Automation Rules: check and execute"
msgstr "Règles d'automatisation : vérification et exécution"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
msgid "Automations"
msgstr "Automatisations"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on date field"
msgstr "Sur la base du champ de date"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "Avant la mise à jour du domaine"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Change the URL's secret if you think the URL is no longer secure. You will "
"have to update any automated system that calls this webhook to the new URL."
msgstr ""
"Modifiez le secret de l'URL si vous pensez que l'URL n'est plus sécurisée. "
"Vous devez mettre à jour tout système automatisé qui appel ce webhook avec "
"la nouvelle URL."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Compute"
msgstr "Calculer"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Create %(model_name)s with name %(value)s"
msgstr "Créez %(model_name)s avec le nom %(value)s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Create a new Record"
msgstr "Créez un nouvel enregistrement"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Create activity: %(activity_name)s"
msgstr "Créez une activité : %(activity_name)s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Create next activity"
msgstr "Créez l'activité suivante"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "Créé le"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Custom"
msgstr "Personnalisé"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Date Field"
msgstr "Champ date"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
msgid "Days"
msgstr "Jours"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delay"
msgstr "Délai"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date. You can put a negative number if you need a "
"delay before the trigger date, like sending a reminder 15 minutes before a "
"meeting."
msgstr ""
"Délai après la date de déclenchement. Vous pouvez saisir une valeur négative"
" si vous avez besoin d'un délai avant la date de déclenchement, comme "
"l'envoi d'un rappel 15 minutes avant une réunion."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "Délai après la date de déclenchement"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "Type de délai"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delete Action"
msgstr "Supprimer l'action"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Deprecated (do not use)"
msgstr "Obsolète (ne pas utiliser)"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__description
msgid "Description"
msgstr "Description"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid "Disable Automation Rule"
msgstr "Désactiver la règle d'automatisation"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid ""
"Disabling this automation rule will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""
"La désactivation de cette règle d'automatisation vous permet de continuer votre flux de travail\n"
"mais toutes les données créées par la suite pourraient être corrompues, \n"
"car vous désactivez une personnalisation qui peut définir\n"
"des champs importantes et/ou obligatoires."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid "Edit Automation Rule"
msgstr "Modifier la règle d'automatisation"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Email"
msgstr "Emai"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Email Events"
msgstr "E-mail Evénéments"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"Email, follower or activity action types cannot be used when deleting "
"records, as there are no more records to apply these changes to!"
msgstr ""
"Les types d'action e-mail, abonné ou activité ne peuvent pas être utilisés "
"lors de la suppression d'enregistrements, car il n'y a plus "
"d'enregistrements sur lesquels appliquer ces changements !"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Execute Python Code"
msgstr "Exécuter le code Python"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Execute several actions"
msgstr "Exécuter plusieurs actions"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "External"
msgstr "Externe"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Extra Conditions"
msgstr "Conditions supplémentaires"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "Champs qui déclenchent l'onchange."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Generic User"
msgstr "Utilisateur générique"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_is_mail_thread
msgid "Has Mail Thread"
msgstr "A un fil de messagerie"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "Heures"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the automation"
" rule."
msgstr ""
"Si présente, la condition doit être satisfaite avant d'exécuter la règle "
"d'automatisation."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record. Not checked on record creation."
msgstr ""
"Si elle est présente, cette condition doit être remplie avant la mise à jour"
" de l'enregistrement. Ceci n'est pas vérifié lors de la création de "
"l'enregistrement."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Keep track of what this automation does and why it exists..."
msgstr ""
"Gardez la trace de ce que fait cette automatisation et de sa raison "
"d'être..."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "Dernière exécution"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr "Délai minimum Msg"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__log_webhook_calls
msgid "Log Calls"
msgstr "Enregistrer les appels"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Logs"
msgstr "Journaux"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"Mail event can not be configured on model %s. Only models with discussion "
"feature can be used."
msgstr ""
"E-mail événement ne peut pas être configuré sur le modèle %s. Seuls les "
"modèles dotés de la fonction de discussion peuvent être utilisés."

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "Minutes"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "Modèle"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "Nom de modèle"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid ""
"Model of action %(action_name)s should match the one from automated rule "
"%(rule_name)s."
msgstr ""
"Le modèle de l'action %(action_name)s doit correspondre au modèle de la "
"règle automatisée %(rule_name)s."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the automation rule runs."
msgstr "Modèle sur lequel se base l'exécution de la règle d'automatisation."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
msgid "Months"
msgstr "Mois"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid "No record to run the automation on was found."
msgstr ""
"Aucun enregistrement sur lequel exécuter l'automatisation n'est trouvé."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"Note that this automation rule can be triggered up to %d minutes after its "
"schedule."
msgstr ""
"Notez que cette règle d'automatisation peut être déclenchée jusqu'au %d "
"minutes après sa programmation."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Notes"
msgstr "Notes"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "Déclenchement des champs de modification"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "On UI change"
msgstr "Lors de la modification de l'interface utilisateur"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_archive
msgid "On archived"
msgstr "Lors de l'archivage"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On creation"
msgstr "Lors de la création"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On deletion"
msgstr "Lors de la suppression"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_received
msgid "On incoming message"
msgstr "En cas de message entrant"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_sent
msgid "On outgoing message"
msgstr "En cas de message sortant"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On save"
msgstr "Lors de l'enregistrement"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unarchive
msgid "On unarchived"
msgstr "Lors du désarchivage"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On update"
msgstr "Lors de la mise à jour"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_webhook
msgid "On webhook"
msgstr "Sur webhook"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Post as Message"
msgstr "Publier en tant que message"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Post as Note"
msgstr "Publier en tant que note"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_priority_set
msgid "Priority is set to"
msgstr "La priorité est définie sur"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__record_getter
msgid "Record Getter"
msgstr "Getter de l'enregistrement"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Remove followers"
msgstr "Supprimer des abonnés"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Remove followers: %(partner_names)s"
msgstr "Supprimer des abonnés : %(partner_names)s"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Rotate Secret"
msgstr "Rotation du secret"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a date field..."
msgstr "Sélectionner un champ de date..."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a value..."
msgstr "Sélectionner une valeur..."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select fields..."
msgstr "Sélectionner des champs..."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Send SMS"
msgstr "Envoyer un SMS"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Send SMS: %(template_name)s"
msgstr "Envoyer un SMS : %(template_name)s"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Send Webhook Notification"
msgstr "Envoyer une notification webhook"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Send an email when an object changes state, archive records\n"
"                after a month of inactivity or remind yourself to follow-up on\n"
"                tasks when a specific tag is added."
msgstr ""
"Envoyez un e-mail lorsque le statut d'un objet change, archivez les enregistrements\n"
"après un mois d'inactivité ou rappelez-vous de suivre\n"
"les tâches lorsqu'une étiquette spécifique est ajoutée."

#. module: base_automation
#: model_terms:digest.tip,tip_description:base_automation.digest_tip_base_automation_0
msgid ""
"Send an email when an object changes state, archive records after a month of"
" inactivity or remind yourself to follow-up on tasks when a specific tag is "
"added.<br>With Automation Rules, you can automate any workflow."
msgstr ""
"Envoyez un e-mail lorsqu'un objet change d'état, archivez les "
"enregistrements après un mois d'inactivité ou rappelez-vous de suivre les "
"tâches lorsqu'une étiquette spécifique est ajoutée.<br>Les règles "
"d'automatisation permettent d'automatiser n'importe quel flux de travail."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Send email"
msgstr "Envoyer un e-mail"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Send email: %(template_name)s"
msgstr "Envoyer un e-mail : %(template_name)s"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "Action de serveur"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_selection_field_id
msgid ""
"Some triggers need a reference to a selection field. This field is used to "
"store it."
msgstr ""
"Certains déclencheurs ont besoin d'une référence à un champ de sélection. Ce"
" champ est utiliser pour la stocker."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_field_ref
msgid ""
"Some triggers need a reference to another field. This field is used to store"
" it."
msgstr ""
"Certains déclencheurs ont besoin d'une référence à un autre champ. Ce champ "
"est utilisé pour la stocker."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Specific User"
msgstr "Utilisateur spécifique"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_stage_set
msgid "Stage is set to"
msgstr "L'étape est définie sur"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
msgid "Stage is set to \"%s\""
msgstr "L'étape est définie sur \"%s\""

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_state_set
msgid "State is set to"
msgstr "Le statut est défini sur"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_tag_set
msgid "Tag is added"
msgstr "L'étiquette est ajoutée"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Target Record"
msgstr "Enregistrement cible"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"Target model of actions %(action_names)s are different from rule model."
msgstr ""
"Le modèle cible des actions %(action_names)s est différent du modèle des "
"règles."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"Le \"%(trigger_value)s\" %(trigger_label)s ne peut être utilisé qu'avec le "
"type d'action \"%(state_value)s\""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The automation rule will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr ""
"La règle d'automatisation sera déclenchée si et seulement si l'un de ces "
"champs est mis à jour. Si vide, tous les champs sont surveillés."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid ""
"The error occurred during the execution of the automation rule\n"
"                \""
msgstr ""
"L'erreur s'est produite lors de l'exécution de la règle d'automatisation\n"
"                \""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__record_getter
msgid ""
"This code will be run to find on which record the automation rule should be "
"run."
msgstr ""
"Ce code sera exécuté pour découvrir sur quel enregistrement la règle "
"automatisation doit être exécutée."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Timing Conditions"
msgstr "Conditions temporelles"

#. module: base_automation
#: model:digest.tip,name:base_automation.digest_tip_base_automation_0
#: model_terms:digest.tip,tip_description:base_automation.digest_tip_base_automation_0
msgid "Tip: Automate everything with Automation Rules"
msgstr "Automatisez tout grâce aux règles d'automatisation"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "Déclencher"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "Date de déclenchement"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_selection_field_id
msgid "Trigger Field"
msgstr "Champ de déclenchement"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref_model_name
msgid "Trigger Field Model"
msgstr "Modèle de champ de déclenchement"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
msgid "Trigger Fields"
msgstr "Champs de déclenchement"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref
msgid "Trigger Reference"
msgstr "Référence de déclenchement"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.ir_actions_server_view_form_automation
msgid "Type"
msgstr "Type"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL"
msgstr "URL"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL will be created once the rule is saved."
msgstr "L'URL sera créée dès que la règle est enregistrée."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
msgid "Update"
msgstr "Mettre à jour"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Update the Record"
msgstr "Mettre à jour l'enregistrement"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__url
msgid "Url"
msgstr "URL"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "Utilisation"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "Utiliser le calendrier"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_user_set
msgid "User is set"
msgstr "L'utilisateur est défini"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
msgid "Values Updated"
msgstr "Valeurs mises à jour"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid "Warning"
msgstr "Avertissement"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid "Webhook Log"
msgstr "Journal webhook"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid "Webhook Logs"
msgstr "Journaux webhook"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__webhook_uuid
msgid "Webhook UUID"
msgstr "UUID webhook"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
msgid "Weeks"
msgstr "Semaines"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possibleto use a "
"calendar to compute the date based on working days."
msgstr ""
"Lors du calcul d'une condition temporelle basée sur la journée, il est "
"possible d'utiliser un calendrier pour calculer la date basée sur les jours "
"ouvrables."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"Cas où la condition doit être déclenchée.\n"
" Si définie, la valeur sera vérifiée par le planificateur de tâches. Si vide, la valeur sera vérifiée lors d'une création et d'une mise à jour."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "Lorsque décoché, la règle est masquée et ne sera pas exécutée."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "When updating"
msgstr "Lors de la mise à jour"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"With Automation Rules, you can automate\n"
"                <em>any</em> workflow."
msgstr ""
"Avec les règles d'automatisation, vous pouvez automatiser\n"
"                <em>n'importe quel</em> flux de travail."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid ""
"You can ask an administrator to disable or correct this automation rule."
msgstr ""
"Vous pouvez demander à un administrateur de désactiver ou de corriger cette "
"règle automatisée."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
msgid "You can disable this automation rule or edit it to solve the issue."
msgstr ""
"Vous pouvez désactiver cette règle d'automatisation ou la modifier pour "
"résoudre l'incident."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
"Vous ne pouvez pas envoyer d'e-mail, ajouter des abonnés ou créer une "
"activité pour un enregistrement supprimé. Cela ne fonctionne tout simplement"
" pas."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Your webhook URL contains a secret. Don't share it online or carelessly."
msgstr ""
"L'URL de votre webhook contient un secret. Ne la partagez pas en ligne ou "
"sans précaution."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "based on"
msgstr "basé sur"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "e.g. Support flow"
msgstr "par ex. Flux de l'assistance"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.xml:0
msgid "no action defined..."
msgstr "aucune action n'est définie..."
