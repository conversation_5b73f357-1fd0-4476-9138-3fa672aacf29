# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_loyalty_delivery
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/loyalty_reward.py:0
msgid " (Max %s)"
msgstr " (Maks %s)"

#. module: sale_loyalty_delivery
#: model_terms:ir.ui.view,arch_db:sale_loyalty_delivery.loyalty_reward_view_kanban_inherit_loyalty_delivery
msgid "( Max"
msgstr "( Maks"

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/loyalty_program.py:0
msgid "Automatic promotion: free shipping on orders higher than $50"
msgstr "Promosi otomatis: pengiriman gratis pada pesanan di atas $50"

#. module: sale_loyalty_delivery
#: model:ir.model.fields.selection,name:sale_loyalty_delivery.selection__loyalty_reward__reward_type__shipping
msgid "Free Shipping"
msgstr "Bebas Biaya Kirim"

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/sale_order.py:0
msgid "Free Shipping - %s"
msgstr "Pengiriman Gratis - %s"

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/loyalty_reward.py:0
#: model_terms:ir.ui.view,arch_db:sale_loyalty_delivery.loyalty_reward_view_form_inherit_loyalty_delivery
#: model_terms:ir.ui.view,arch_db:sale_loyalty_delivery.loyalty_reward_view_kanban_inherit_loyalty_delivery
msgid "Free shipping"
msgstr "Pengiriman grati"

#. module: sale_loyalty_delivery
#: model:ir.model,name:sale_loyalty_delivery.model_loyalty_program
msgid "Loyalty Program"
msgstr "Program Loyalitas"

#. module: sale_loyalty_delivery
#: model:ir.model,name:sale_loyalty_delivery.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "Hadiah Loyalitas"

#. module: sale_loyalty_delivery
#: model:ir.model.fields,field_description:sale_loyalty_delivery.field_loyalty_reward__reward_type
msgid "Reward Type"
msgstr "Tipe Hadiah"

#. module: sale_loyalty_delivery
#: model:ir.model,name:sale_loyalty_delivery.model_sale_order
msgid "Sales Order"
msgstr "Order Penjualan"
