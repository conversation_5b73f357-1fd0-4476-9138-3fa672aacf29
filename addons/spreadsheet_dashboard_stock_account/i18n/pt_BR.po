# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_stock_account
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "3D printing pen"
msgstr "Caneta de impressão 3D"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "447 out of 1,856"
msgstr "447 de 1.856"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "76,437 out of 188,071"
msgstr "76.437 de 188.071"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ageing stock qty by category and creation date"
msgstr "Quantidade de estoque envelhecido por categoria e data de criação"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ageing stock value by product and creation date"
msgstr "Valor do estoque envelhecido por produto e data de criação"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available Quantity"
msgstr "Quantidade disponível"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available Value"
msgstr "Valor disponível"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock qty (top locations)"
msgstr "Quantidade de estoque disponível e reservado (principais locais)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock qty (top propducts)"
msgstr "Quantidade de estoque disponível e reservada (principais produtos)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock value (top locations)"
msgstr "Valor do estoque disponível e reservado (principais locais)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock value (top propducts)"
msgstr "Valor do estoque disponível e reservado (principais produtos)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Bluetooth-enabled LED light strip"
msgstr "Fota de luz LED com Bluetooth"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Compact espresso machine"
msgstr "Máquina de café expresso compacta"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Count of products with negative stock"
msgstr "Total de produtos com estoque negativo"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Electric standing desk"
msgstr "Escrevaninha de pé elétrica"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ergonomic office chair"
msgstr "Cadeira de escritório ergonômica"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Lines with negative stock"
msgstr "Linhas com estoque negativo"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Location"
msgstr "Local"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Lot/Serial"
msgstr "Lote/série"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Post-production"
msgstr "Pós-produção"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Pre-production"
msgstr "Pré-produção"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Product"
msgstr "Produto"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Product Category"
msgstr "Categoria de produtos"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Products"
msgstr "Produtos"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved"
msgstr "Reservado"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved Quantity"
msgstr "Quantidade reservada"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved Value"
msgstr "Valor reservado"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share of reserved stock Value"
msgstr "Parcela de estoque reservado - Valor"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share of reserved stock qty"
msgstr "Parcela de estoque reservado - Qntd."

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share reserved stock Qty"
msgstr "Dividir qntd. de estoque reservado"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share reserved stock Value"
msgstr "Dividir valor de estoque reservado"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Smart air purifier"
msgstr "Purificador de ar inteligente"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Solar-powered phone charger"
msgstr "Carregador de celular co energia solar"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Top 10 products with negative stock"
msgstr "Os 10 principais produtos com estoque negativo"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Total"
msgstr "Total"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Total inventory value"
msgstr "Valor total do inventário"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Output"
msgstr "WH/Output"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Stock"
msgstr "WH/Stock"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Stock/Shelf 10"
msgstr "WH/Stock/Shelf 10"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Warehouse"
msgstr "Armazém"

#. module: spreadsheet_dashboard_stock_account
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_stock_account.spreadsheet_dashboard_warehouse_metrics
msgid "Warehouse Metrics"
msgstr "Métricas de armazém"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Waterproof hiking backpack"
msgstr "Mochila de trilha à prova d'água"
