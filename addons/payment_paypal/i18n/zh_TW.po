# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_paypal
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Client ID"
msgstr "客戶端 ID"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Client Secret"
msgstr "客戶秘鑰"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__code
msgid "Code"
msgstr "代碼"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "未能建立與 API 的連線。"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "Could not generate a new access token."
msgstr "未能產生新的存取權杖。"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_email_account
msgid "Email"
msgstr "電郵"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Generate your webhook"
msgstr "產生你的網絡鈎子（webhook）"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "How to configure your paypal account?"
msgstr "如何設定您的Paypal帳戶？"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/controllers/main.py:0
msgid "Invalid response format, can't normalize."
msgstr "回應格式無效，未能正常化。"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "Missing value for txn_id (%(txn_id)s) or txn_type (%(txn_type)s)."
msgstr "txn_id (%(txn_id)s) 或 txn_type (%(txn_type)s) 缺少值。"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "沒有找到匹配參考 %s 的交易。"

#. module: payment_paypal
#: model:ir.model.fields.selection,name:payment_paypal.selection__payment_provider__code__paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_access_token
msgid "PayPal Access Token"
msgstr "PayPal 存取權杖"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_access_token_expiry
msgid "PayPal Access Token Expiry"
msgstr "PayPal 存取權杖到期日"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_client_id
msgid "PayPal Client ID"
msgstr "PayPal 客戶端識別碼"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_client_secret
msgid "PayPal Client Secret"
msgstr "PayPal 客戶端秘密"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_type
msgid "PayPal Transaction Type"
msgstr "PayPal 交易類型"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_webhook_id
msgid "PayPal Webhook ID"
msgstr "PayPal 網絡鈎子識別碼"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_provider
msgid "Payment Provider"
msgstr "支付提供商"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_paypal
#. odoo-javascript
#: code:addons/payment_paypal/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "付款處理失敗"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "Received data with invalid payment status: %s"
msgstr "收到的付款狀態無效的資料：%s"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "The communication with the API failed. Details: %s"
msgstr "與 API 通訊失敗。詳情：%s"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "The customer left the payment page."
msgstr "客戶離開了付款頁面。"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_access_token_expiry
msgid "The moment at which the access token becomes invalid."
msgstr "存取權杖失效的時刻。"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_email_account
msgid ""
"The public business email solely used to identify the account with PayPal"
msgstr "僅用於識別 PayPal 帳戶的公共企業電子郵件"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_access_token
msgid "The short-lived token used to access Paypal APIs"
msgstr "用作存取 PayPal API 的短期有效權杖"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "此付款服務商的技術代碼。"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Webhook ID"
msgstr "網絡鈎子識別碼"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "You must have an HTTPS connection to generate a webhook."
msgstr "必須使用 HTTPS 連線，才可產生網絡鈎子。"
