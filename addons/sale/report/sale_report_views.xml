<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_order_product_pivot" model="ir.ui.view">
         <field name="name">sale.report.pivot</field>
         <field name="model">sale.report</field>
         <field name="arch" type="xml">
             <pivot string="Sales Analysis" sample="1">
                 <field name="team_id" type="col"/>
                 <field name="date" interval="month" type="row"/>
                 <field name="product_uom_qty" type="measure"/>
             </pivot>
         </field>
    </record>

    <record id="view_order_product_graph" model="ir.ui.view">
         <field name="name">sale.report.graph</field>
         <field name="model">sale.report</field>
         <field name="arch" type="xml">
             <graph string="Sales Analysis" type="line" sample="1">
                 <field name="date" interval="month"/>
                 <field name="product_uom_qty" type="measure"/>
             </graph>
         </field>
    </record>

    <record id="sale_report_graph_pie" model="ir.ui.view">
         <field name="name">sale.report.graph.pie</field>
         <field name="model">sale.report</field>
         <field name="mode">primary</field>
         <field name="inherit_id" ref="view_order_product_graph"/>
         <field name="arch" type="xml">
            <graph position="attributes">
                <attribute name="type">pie</attribute>
            </graph>
         </field>
    </record>

     <record id="sale_report_graph_bar" model="ir.ui.view">
         <field name="name">sale.report.graph.bar</field>
         <field name="model">sale.report</field>
         <field name="mode">primary</field>
         <field name="inherit_id" ref="view_order_product_graph"/>
         <field name="arch" type="xml">
            <graph position="attributes">
                <attribute name="type">bar</attribute>
                <attribute name="order">DESC</attribute>
            </graph>
         </field>
    </record>

    <record id="sale_report_view_tree" model="ir.ui.view">
        <field name="name">sale.report.view.list</field>
        <field name="model">sale.report</field>
        <field name="arch" type="xml">
            <list string="Sales Analysis" action="action_open_order" type="object">
                <field name="date"/>
                <field name="order_reference" optional="show"/>
                <field name="product_id" string="Product" optional="show"/>
                <field name="partner_id"/>
                <field name="user_id" optional="show" widget="many2one_avatar_user"/>
                <field name="team_id" optional="hide"/>
                <field name="company_id" optional="show" groups="base.group_multi_company"/>
                <field name="product_uom_qty" string="Quantity" sum="Sum of Quantity"/>
                <field name="price_subtotal" optional="hide" sum="Sum of Untaxed Total"/>
                <field name="price_unit" widget="monetary" avg="Average"/>
                <field name="price_total" optional="show" sum="Sum of Total"/>
                <field name="state" optional="hide"/>
                <field name="pricelist_id" optional="hide"/>
                <field name="line_invoice_status" optional="hide"/>
                <field name="currency_id" column_invisible="True"/>
            </list>
        </field>
    </record>

    <record id="view_order_product_search" model="ir.ui.view">
        <field name="name">sale.report.search</field>
        <field name="model">sale.report</field>
        <field name="arch" type="xml">
            <search string="Sales Analysis">
                <field name="date"/>
                <filter string="Date" name="year" invisible="1" date="date" default_period="year"/>
                <filter string="Quotations" name="Quotations" domain="[('state','in', ('draft', 'sent'))]"/>
                <filter string="Sales Orders" name="Sales" domain="[('state','not in',('draft', 'cancel', 'sent'))]"/>
                <separator/>
                <filter name="filter_date" date="date" default_period="month"/>
                <filter name="filter_order_date" invisible="1" string="Order Date: Last 365 Days" domain="[('date', '&gt;=', (datetime.datetime.combine(context_today() + relativedelta(days=-365), datetime.time(0,0,0))).strftime('%Y-%m-%d %H:%M:%S'))]"/>
                <separator/>
                <field name="user_id"/>
                <field name="team_id"/>
                <field name="product_id"/>
                <field name="product_tmpl_id"/>
                <field name="categ_id"/>
                <filter name="to_invoice" string="To Invoice" domain="[('line_invoice_status', '=', 'to invoice')]"/>
                <filter name="fully_invoiced" string="Fully Invoiced" domain="[('line_invoice_status', '=', 'invoiced')]"/>
                <field name="partner_id"/>
                <field name="country_id"/>
                <field name="industry_id"/>
                <group expand="0" string="Extended Filters">
                    <field name="categ_id" filter_domain="[('categ_id', 'child_of', self)]"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </group>
                <group expand="1" string="Group By">
                    <filter string="Salesperson" name="User" context="{'group_by':'user_id'}"/>
                    <filter string="Sales Team" name="sales_channel" context="{'group_by':'team_id'}"/>
                    <filter string="Customer" name="Customer" context="{'group_by':'partner_id'}"/>
                    <filter string="Customer Country" name="country_id" context="{'group_by':'country_id'}"/>
                    <filter string="Customer Industry" name="industry_id" context="{'group_by':'industry_id'}"/>
                    <filter string="Product" name="product_tmpl_id" context="{'group_by':'product_tmpl_id'}"/>
                    <filter string="Product Variant" name="product_id" context="{'group_by':'product_id'}"
                            groups="product.group_product_variant"/>
                    <filter string="Product Category" name="Category" context="{'group_by':'categ_id'}"/>
                    <filter string="Status" name="status" context="{'group_by':'state'}"/>
                    <filter string="Company" name="company" groups="base.group_multi_company" context="{'group_by':'company_id'}"/>
                    <separator/>
                    <filter string="Order Date" name="group_by_date" context="{'group_by':'date'}"
                            invisible="context.get('sale_report_view_hide_date')"/>
                    <filter string="Order Date" name="group_by_date_day" context="{'group_by':'date:day'}"
                            invisible="not context.get('sale_report_view_hide_date')"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_order_report_all" model="ir.actions.act_window">
        <field name="name">Sales Analysis</field>
        <field name="res_model">sale.report</field>
        <field name="view_mode">graph,pivot,list,form</field>
        <field name="view_id"></field>  <!-- force empty -->
        <field name="search_view_id" ref="view_order_product_search"/>
        <field name="domain">[('state', '!=', 'cancel')]</field>
        <field name="context">{'search_default_Sales':1,'group_by':[], 'search_default_filter_order_date': 1}</field>
        <field name="help">This report performs analysis on your quotations and sales orders. Analysis check your sales revenues and sort it by different group criteria (salesman, partner, product, etc.) Use this report to perform analysis on sales not having invoiced yet. If you want to analyse your turnover, you should use the Invoice Analysis report in the Accounting application.</field>
    </record>

    <record id="action_order_report_salesperson" model="ir.actions.act_window">
        <field name="name">Sales Analysis By Salespersons</field>
        <field name="res_model">sale.report</field>
        <field name="view_mode">graph,pivot</field>
        <field name="view_id" ref="sale_report_graph_bar"/>
        <field name="search_view_id" ref="view_order_product_search"/>
        <field name="context">{'search_default_User': 1, 'group_by': 'user_id', 'search_default_filter_order_date': 1}</field>
        <field name="help">This report performs analysis on your quotations and sales orders. Analysis check your sales revenues and sort it by different group criteria (salesman, partner, product, etc.) Use this report to perform analysis on sales not having invoiced yet. If you want to analyse your turnover, you should use the Invoice Analysis report in the Accounting application.</field>
    </record>

    <record id="action_order_report_products" model="ir.actions.act_window">
        <field name="name">Sales Analysis By Products</field>
        <field name="res_model">sale.report</field>
        <field name="view_mode">graph,pivot</field>
        <field name="view_id" ref="sale_report_graph_pie"/>
        <field name="search_view_id" ref="view_order_product_search"/>
        <field name="context">{'search_default_Sales': 1, 'search_default_Product': 1, 'group_by': 'product_id', 'search_default_filter_order_date': 1}</field>
        <field name="help">This report performs analysis on your quotations and sales orders. Analysis check your sales revenues and sort it by different group criteria (salesman, partner, product, etc.) Use this report to perform analysis on sales not having invoiced yet. If you want to analyse your turnover, you should use the Invoice Analysis report in the Accounting application.</field>
    </record>

    <record id="action_order_report_customers" model="ir.actions.act_window">
        <field name="name">Sales Analysis By Customers</field>
        <field name="res_model">sale.report</field>
        <field name="view_mode">graph,pivot</field>
        <field name="view_id" ref="sale_report_graph_bar"/>
        <field name="search_view_id" ref="view_order_product_search"/>
        <field name="context">{'search_default_Customer': 1, 'group_by': 'partner_id', 'search_default_filter_order_date': 1}</field>
        <field name="help">This report performs analysis on your quotations and sales orders. Analysis check your sales revenues and sort it by different group criteria (salesman, partner, product, etc.) Use this report to perform analysis on sales not having invoiced yet. If you want to analyse your turnover, you should use the Invoice Analysis report in the Accounting application.</field>
    </record>

    <record id="report_all_channels_sales_action" model="ir.actions.act_window">
        <field name="name">Sales Analysis</field>
        <field name="res_model">sale.report</field>
        <field name="view_mode">list,pivot,graph,form</field>
    </record>

    <record id="action_order_report_quotation_salesteam" model="ir.actions.act_window">
        <field name="name">Quotations Analysis</field>
        <field name="res_model">sale.report</field>
        <field name="view_mode">graph,list</field>
        <field name="domain">[('state','=','draft'),('team_id', '=', active_id)]</field>
        <field name="context">{'search_default_order_month':1}</field>
        <field name="help">This report performs analysis on your quotations. Analysis check your sales revenues and sort it by different group criteria (salesman, partner, product, etc.) Use this report to perform analysis on sales not having invoiced yet. If you want to analyse your turnover, you should use the Invoice Analysis report in the Accounting application.</field>
    </record>

    <record id="action_order_report_so_salesteam" model="ir.actions.act_window">
        <field name="name">Sales Analysis</field>
        <field name="res_model">sale.report</field>
        <field name="view_mode">graph,list</field>
        <field name="domain">[('state','not in',('draft','cancel'))]</field>
        <field name="context">{
            'search_default_Sales': 1,
            'search_default_filter_date': 1,
            'search_default_team_id': [active_id]}</field>
        <field name="help">This report performs analysis on your sales orders. Analysis check your sales revenues and sort it by different group criteria (salesman, partner, product, etc.) Use this report to perform analysis on sales not having invoiced yet. If you want to analyse your turnover, you should use the Invoice Analysis report in the Accounting application.</field>
    </record>
</odoo>
