<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="im_livechat.unit_embed_suite">
        <t t-call="web.layout">
            <t t-set="html_data" t-value="{'style': 'height: 100%;'}"/>
            <t t-set="title">Livechat External Tests</t>
            <t t-set="head">
                <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>

                <script type="text/javascript">
                    odoo.__session_info__ = <t t-out="json.dumps(session_info)"/>;
                </script>

                <t t-call-assets="im_livechat.embed_assets_unit_tests_setup" />
                <t t-call-assets="im_livechat.embed_assets_unit_tests" />
            </t>
        </t>
    </template>
</odoo>
