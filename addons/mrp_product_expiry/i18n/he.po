# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_product_expiry
# 
# Translators:
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: mrp_product_expiry
#: model_terms:ir.ui.view,arch_db:mrp_product_expiry.confirm_expiry_view_mrp_inherit
msgid "Confirm"
msgstr "אשר"

#. module: mrp_product_expiry
#: model:ir.model,name:mrp_product_expiry.model_expiry_picking_confirmation
msgid "Confirm Expiry"
msgstr "אישור תפוגה"

#. module: mrp_product_expiry
#. odoo-python
#: code:addons/mrp_product_expiry/models/mrp_production.py:0
msgid "Confirmation"
msgstr "אישור"

#. module: mrp_product_expiry
#: model_terms:ir.ui.view,arch_db:mrp_product_expiry.confirm_expiry_view_mrp_inherit
msgid "Discard"
msgstr "בטל"

#. module: mrp_product_expiry
#: model:ir.model,name:mrp_product_expiry.model_mrp_production
msgid "Manufacturing Order"
msgstr "הוראת ייצור"

#. module: mrp_product_expiry
#: model:ir.model.fields,field_description:mrp_product_expiry.field_expiry_picking_confirmation__production_ids
msgid "Production"
msgstr "ייצור"

#. module: mrp_product_expiry
#: model:ir.model.fields,field_description:mrp_product_expiry.field_expiry_picking_confirmation__workorder_id
msgid "Workorder"
msgstr "הוראת עבודה"

#. module: mrp_product_expiry
#. odoo-python
#: code:addons/mrp_product_expiry/wizard/confirm_expiry.py:0
msgid ""
"You are going to use some expired components.\n"
"Do you confirm you want to proceed?"
msgstr ""

#. module: mrp_product_expiry
#. odoo-python
#: code:addons/mrp_product_expiry/wizard/confirm_expiry.py:0
msgid ""
"You are going to use the component %(product_name)s, %(lot_name)s which is expired.\n"
"Do you confirm you want to proceed?"
msgstr ""
