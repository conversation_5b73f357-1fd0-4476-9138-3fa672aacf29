# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_count
msgid "# Questions"
msgstr "# Preguntas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "# Questions Randomly Picked"
msgstr "# Preguntas seleccionadas al azar"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "100 €"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "20 €"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "200 €"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "300 €"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "50 €"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "80 €"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% completada"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "%(participant)s just participated in \"%(survey_title)s\"."
msgstr "%(participant)s acaba de participar en „%(survey_title)s“."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s certification passed"
msgstr "%s certificación aprobada"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s challenge certification"
msgstr "%s certificación de desafío"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "'Certificación: %s' % (object.survey_id.display_name)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "0000000010"
msgstr "0000000010"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 años"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 años"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127 años"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16,2 kg"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "2023-08-18"
msgstr "18-08-2023"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 km"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403: Prohibido"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5.7 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 años"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>Certificado</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Completed</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Completado</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Registered</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Registrado</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<br/> <span>by</span>"
msgstr "<br/> <span>por</span>"

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-if=\"not object.survey_id.create_uid.company_id.uses_default_logo\" t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Please find attached your\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Furniture Creation</strong>\n"
"                    certification\n"
"                </p>\n"
"                <p>Congratulations for passing the test with a score of <strong t-out=\"object.scoring_percentage\"></strong>%!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- Usamos el logotipo de la compañía que realizó la encuesta (para gestionar casos de multiempresas) -->\n"
"                <a href=\"/\"><img t-if=\"not object.survey_id.create_uid.company_id.uses_default_logo\" t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certificación: <t t-out=\"object.survey_id.display_name or ''\">Formulario de retroalimentación</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Estimado/a <span t-out=\"object.partner_id.name or 'participant'\">participante</span></p>\n"
"                <p>\n"
"                    A continuación encontrará adjunta su certificación de\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Creación de mobiliario</strong>\n"
"                    .\n"
"                </p>\n"
"                <p>¡Enhorabuena por aprobar el cuestionario con una puntuación de <strong t-out=\"object.scoring_percentage\"></strong> %!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br><br>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br><br>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            We wish you good luck!\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Thank you in advance for your participation.\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hola <t t-out=\"object.partner_id.name or 'participant'\">participante</t><br><br>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            Le invitamos a realizar un nueva certificación.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Estamos realizando una encuesta y agradeceríamos su participación.\n"
"        </t>\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Iniciar certificación\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Iniciar encuesta\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Responda la encuesta antes del <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br><br>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            ¡Buena suerte!\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Le agradecemos de antemano su participación.\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "<i class=\"fa fa-bar-chart\"/> Resultados"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg me-2\"/>respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg me-2\"/>respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "<i class=\"fa fa-close\"/> Cerrar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> It is currently not possible to "
"pass this assessment because no question is configured to give any points."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>No es posible aprobar esta "
"evaluación porque ninguna pregunta está diseñada para otorgar puntos."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Descargar certificación"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-square-o fa-lg me-2\"/>respuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Go to Survey"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>Ir a la encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Avg</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Promedio</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Max</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 pt-1\">Máximo "
"</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Min</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Mínimo</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> or press CTRL+Enter</span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> o presionar CTRL-Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">or press Enter</span>\n"
"                        </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">o presionar Enter</span>\n"
"                        </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                    <span id=\"enter-tooltip\">or press CTRL+Enter</span>\n"
"                </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                    <span id=\"enter-tooltip\">o presionar CTRL+Enter</span>\n"
"                </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"mx-1\">-</span>"
msgstr "<span class=\"mx-1\">-</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_company_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_company_count &gt; 1\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"certifications_company_count &lt; 2\">Certificaciones</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_company_count &gt; 1\">Certificación</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_count &gt; 1\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"certifications_count &lt; 2\">Certificaciones</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_count &gt; 1\">Certificación</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-inline\">or"
" press Enter</span>"
msgstr ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-inline\">o "
"presionar Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Clave</span></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid ""
"<span class=\"o_survey_results_topbar_clear_filters text-primary\">\n"
"                                <i class=\"fa fa-trash me-1\"/>Remove all filters\n"
"                            </span>"
msgstr ""
"<span class=\"o_survey_results_topbar_clear_filters text-primary\">\n"
"                                <i class=\"fa fa-trash me-1\"/>Eliminar todos los filtros\n"
"                            </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid ""
"<span class=\"o_survey_session_navigation_next_label\">Start</span>\n"
"                        <i class=\"oi oi-chevron-right\"/>"
msgstr ""
"<span class=\"o_survey_session_navigation_next_label\">Comenzar</span>\n"
"                        <i class=\"oi oi-chevron-right\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Completed</span>"
msgstr "<span class=\"text-break text-muted\">Completadas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Registered</span>"
msgstr "<span class=\"text-break text-muted\">Registradas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Success</span>"
msgstr "<span class=\"text-break text-muted\">Exitosas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Average Duration</span>"
msgstr "<span class=\"text-muted\">Duración media</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Questions</span>"
msgstr "<span class=\"text-muted\">Preguntas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-muted\">Responded</span>"
msgstr "<span class=\"text-muted\">Respondido</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-success\">Correct</span>"
msgstr "<span class=\"text-success\">Correcto</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-warning\">Partial</span>"
msgstr "<span class=\"text-warning\">Parcial</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span invisible=\"not is_scored_question\">Points</span>"
msgstr "<span invisible=\"not is_scored_question\">Puntos</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr "<span>\"Rojo\" no es una categoría, sé lo que intenta hacer ;)</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>All surveys</span>"
msgstr "<span>Todas las encuestas</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr ""
"<span>El mejor momento para hacerlo, es el momento adecuado para "
"hacerlo.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Completed surveys</span>"
msgstr "<span>Encuestas completadas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<span>Date</span>"
msgstr "<span>Fecha</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Do you like it?</span><br/>"
msgstr "<span>¿Le gusta?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Failed only</span>"
msgstr "<span>Solo no aprobadas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>How many ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"
msgstr ""
"<span>¿Cuántos ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numérico\" title=\"Numérico\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numérico\"/>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr "<span>Si no le agradamos, intente ser lo más objetivo posible.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name all the animals</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"
msgstr ""
"<span>Nombre todos los animales</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name one animal</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"
msgstr ""
"<span>Nombre un animal</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attempts left</span>:"
msgstr "<span>Número de intentos restantes</span>:"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader!</span>"
msgstr "<span>¡Nuestro famoso líder!</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it!</span>"
msgstr ""
"<span>Nuestro personal de ventas tiene una ventaja, ¡pero usted puede "
"hacerlo!</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed and Failed</span>"
msgstr "<span>Aprobadas y no aprobadas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed only</span>"
msgstr "<span>Solo aprobadas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>This certificate is presented to</span>\n"
"                                <br/>"
msgstr ""
"<span>Este certificado se otorga a</span>\n"
"                                <br/>"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "<span>Try It</span>"
msgstr "<span>Pruébelo</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>Esperando asistentes ...</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When does ... start?</span><br/>"
msgstr "<span>¿Cuándo empieza...?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When is Christmas?</span><br/>"
msgstr "<span>¿Cuándo es Navidad?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which are yellow?</span><br/>"
msgstr "<span>¿Cuáles son amarillos?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which is yellow?</span><br/>"
msgstr "<span>¿Cuál es amarillo?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>for successfully completing</span>\n"
"                                <br/>"
msgstr ""
"<span>por haber completado con éxito</span>\n"
"                                <br/>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "Un \"Citrus\" podría darte ..."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "A label must be attached to only one question."
msgstr "Se debe adjuntar una etiqueta a una sola pregunta."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "¡Una longitud debe ser positiva!"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "Un poco caro"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "Demasiado caro"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_speed_rating_has_time_limit
msgid ""
"A positive default time limit is required when the session rewards quick "
"answers."
msgstr ""
"Se necesita un límite de tiempo positivo por defecto cuando la sesión da "
"recompensas por respuestas rápidas."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"Una puntuación positiva indica una respuesta correcta, una negativa o nula "
"indica una respuesta incorrecta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "Ocurrió un problema"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "A question can either be skipped or answered, not both."
msgstr "Las preguntas se pueden omitir o responder, no las dos."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"A scored survey needs at least one question that gives points.\n"
"Please check answers and their scores."
msgstr ""
"Una encuesta con puntuación necesita al menos una pregunta que otorgue puntos.\n"
"Compruebe las respuestas y sus puntuaciones."

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "Sobre nuestro comercio electrónico"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "Sobre usted"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "Modo de acceso"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "El token de acceso debe ser único"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "Activo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad de Excepción"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actvidad"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a question"
msgstr "Agregar una pregunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a section"
msgstr "Agregar una sección"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "Agregar contactos existentes..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add some fun to your presentations by sharing questions live"
msgstr ""
"Sus presentaciones pueden ser más divertidas si hace preguntas en vivo."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "Correos electrónicos adicionales"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "Administrador"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "África"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year?"
msgstr ""
"Después de ver este vídeo, ¿promete que dejará de aplazar la poda de su seto"
" este año?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "De acuerdo"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr ""
"Todas las preguntas \"Es una pregunta con puntuación = True\" y \"Tipo de "
"pregunta: Fecha\" necesitan una respuesta"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr ""
"Todas las preguntas \"Es una pregunta con puntuación = True\" y \"Tipo de "
"pregunta: Fecha y hora\" necesitan una respuesta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "Todas las preguntas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "All surveys"
msgstr "Todas las encuestas"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_is_time_limited_have_time_limit
msgid "All time-limited questions need a positive time limit"
msgstr ""
"Todas las preguntas con límite de tiempo necesitan tener un límite de tiempo"
" positivo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Allow Roaming"
msgstr "Permitir roaming"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__allowed_triggering_question_ids
msgid "Allowed Triggering Questions"
msgstr "Preguntas desencadenantes permitidas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__allowed_survey_types
msgid "Allowed survey types"
msgstr "Tipos de encuestas permitidos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "Amenhotep"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "¡Un token de acceso debe ser único!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr ""
"¡Una puntuación de respuesta para una pregunta de opción no múltiple no "
"puede ser negativa!"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3
msgid "An apple a day keeps the doctor away."
msgstr "Una manzana al día es salud y alegría."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "Anónimo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answer"
msgstr "Respuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "Tipo de respuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "Fecha límite de respuesta"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__value_label
msgid ""
"Answer label as either the value itself if not empty or a letter "
"representing the index of the answer otherwise."
msgstr ""
"La etiqueta de respuesta puede ser el valor en sí si no está vacío o una "
"letra que represente el índice de la respuesta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answers"
msgstr "Respuestas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "Número de respuestas"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "Cualquier persona con el enlace"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Aparece en"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "Manzanos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "Manzanas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "Archivado"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q1
msgid "Are you vegetarian?"
msgstr "¿Sigue una dieta vegetariana?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "Arte y Cultura"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "Arthur B. McDonald"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "Asia"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__assessment
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Assessment"
msgstr "Valoración"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "Archivos adjuntos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "Intento n°"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempts"
msgstr "Intentos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_count
msgid "Attempts Count"
msgstr "Número de intentos"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify them in the survey session "
"leaderboard."
msgstr ""
"Apodo del asistente, se utiliza para identificarle en la tabla de "
"clasificación de la sesión de la encuesta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "Los asistentes están respondiendo la pregunta..."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr "Los asistentes obtienen más puntos si responden rápidamente"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "Autor"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when they succeed the certification, "
"containing their certification document."
msgstr ""
"Correo electrónico automático enviado al usuario cuando aprueba la "
"certificación, contiene el documento de su certificación."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "Otoño"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Average"
msgstr "Promedio"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Duration"
msgstr "Duración media"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Score"
msgstr "Puntuación media"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "Duración media de la encuesta (en horas)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score (%)"
msgstr "Puntuación media (%)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "Avicii"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "Imagen de fondo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image_url
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image_url
msgid "Background Url"
msgstr "URL del fondo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "Insignia"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "Árboles Baobab"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "Abejas"

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_3_question_3
msgid "Beware of leap years!"
msgstr "¡Tenga en cuenta los años bisiestos!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Blue Pen"
msgstr "Bolígrafo azul"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body_has_template_value
msgid "Body content is the same as the template"
msgstr "El contenido del cuerpo es igual al de la plantilla"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "Ladrillos"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_feedback_p1_q1
msgid "Brussels"
msgstr "Bruselas"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q3
msgid "Brussels, Belgium"
msgstr "Bruselas, Bélgica"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "Cuestionario de hamburguesas"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "But first, keep listening to the host."
msgstr "Pero antes, siga escuchando al ponente."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "Armario con puertas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "Cactus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Puede editar el cuerpo"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon?"
msgstr "¿Pueden los humanos llegar a ver directamente un fotón?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
msgid "Certification"
msgstr "Certificación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "Insignia de certificación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "Insignia de certificación "

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr ""
"La insignia de certificación no está configurada para la encuesta "
"%(survey_name)s"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification Failed"
msgstr "Certificación no aprobada"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification n°"
msgstr "Número de la certificación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "Plantilla de certificación"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "Certificación: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "Certificaciones"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "Número de certificaciones"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "Certificaciones exitosas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Certified"
msgstr "Certificado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Certified Email Template"
msgstr "Plantilla de correo electrónico de la certificación"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "Protección del piso de la silla"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Cheating on your neighbors will not help!"
msgstr "Copiar no será de ayuda."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr ""
"Seleccione esta opción si desea limitar el número de intentos por usuario"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/boolean_update_flag_field/boolean_update_flag_fields.js:0
msgid "Checkbox updating comparison flag"
msgstr "Casilla para actualizar el marcador de comparación"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "China"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Opciones"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_burger_quiz
msgid "Choose your favourite subject and show how good you are. Ready?"
msgstr "Elija su tema favorito y demuestre lo bueno que es en ello. ¿Listo?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q3
msgid "Choose your green meal"
msgstr "Elija su comida vegetariana"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q4
msgid "Choose your meal"
msgstr "Elija su comida"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "Azul clásico"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "Oro clásico"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "Morado clásico"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "Clementina"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "Cliff Burton"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close"
msgstr "Cerrar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close Live Session"
msgstr "Cerrar sesión en vivo"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Color"
msgstr "Color"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "Índice de Colores"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Combining roaming and \"Scoring with answers after each page\" is not possible; please update the following surveys:\n"
"- %(survey_names)s"
msgstr ""
"La combinación de roaming y \"Puntuación con respuestas después de cada página\" no es posible; actualice las siguientes encuestas:\n"
"- %(survey_names)s"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "Come back once you have added questions to your Surveys."
msgstr "Regrese una vez que agregue preguntas a su encuesta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "Comentario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "Mensaje del comentario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment is an answer"
msgstr "El comentario es una respuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "Número de certificaciones de la empresa"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "Completada"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Completed surveys"
msgstr "Encuestas completadas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Redactar correo electrónico"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Computing score requires a question in arguments."
msgstr "Calcular la puntuación requiere una pregunta en los argumentos."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Conditional display"
msgstr "Visualización condicional"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"Conditional display is not available when questions are randomly picked."
msgstr ""
"La visualización condicional no está disponible cuando las preguntas se "
"eligen al azar."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "Silla de conferencias"

#. module: survey
#: model_terms:web_tour.tour,rainbow_man_message:survey.survey_tour
msgid "Congratulations! You are now ready to collect feedback like a pro :-)"
msgstr ""
"¡Enhorabuena! Ya está listo para recibir retroalimentación como todo un "
"profesional. :)"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "Enhorabuena, se ha convertido en proveedor de MiCompañía"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "¡Enhorabuena, ha pasado la prueba!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Restricciones"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
msgid "Contact"
msgstr "Contacto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "Contiene preguntas condicionales"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "Contenidos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "Continuar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "Continuar aquí"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "Galletas"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Copied!"
msgstr "¡Copiado!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "Cornaceae"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "Escritorio en L con asiento a la derecha"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
msgid "Correct"
msgstr "Correcto"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "Respuesta correcta"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "Respuesta correcta de fecha y hora para esta pregunta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "Respuesta de fecha correcta"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "Respuesta de fecha correcta para esta pregunta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "Respuesta correcta de fecha y hora"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "Respuesta numérica correcta para esta pregunta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "Respuesta numérica correcta"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "Buen precio"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "Rayos cósmicos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Create Live Session"
msgstr "Crear sesión en vivo"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Create a custom survey from scratch"
msgstr "Crear una encuesta personalizada desde cero"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "Creado el"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating test token is not allowed for you."
msgstr "No se permite crear token de prueba."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr ""
"No se permite la creación de tokens para nadie más que los empleados para "
"las encuestas internas."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating token for closed/archived surveys is not allowed."
msgstr ""
"No está permitido crear tokens para encuestas terminadas o archivadas."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr ""
"No se permite la creación de tokens para personas externas para las "
"encuestas que solicitan autenticación."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "Pregunta actual"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "Hora de inicio de la pregunta actual"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "Hora de inicio de la sesión actual"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "Actualmente solo se admite para sesiones en vivo."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__custom
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Custom"
msgstr "Personalizado"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr ""
"Los clientes recibirán un nuevo token y podrán volver a realizar la encuesta"
" por completo."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "Los clientes recibirán el mismo token."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "Lámpara personalizable"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_customized
msgid "Customized speed rewards"
msgstr "Recompensas personalizadas para rapidez"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "DEMO_CERTIFIED_NAME"
msgstr "DEMO_CERTIFIED_NAME"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "Fecha"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "Respuesta de fecha"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "Fecha y hora"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "Respuesta de fecha y hora"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr ""
"Fecha y hora hasta que el cliente pueda abrir la encuesta y enviar "
"respuestas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "Fecha límite"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Default time given to receive additional points for right answers"
msgstr ""
"El tiempo por defecto para recibir puntos adicionales por las respuestas "
"correctas"

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Defina la visibilidad del desafío a través de los menús"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Description"
msgstr "Descripción"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "Combinación de escritorio"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "Respuestas detalladas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "En desacuerdo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Display Progress as"
msgstr "Mostrar progreso como"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid "Displayed if \"%s\"."
msgstr "Mostrado si \"%s\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Displayed when the answer entered is not valid."
msgstr "Mostrado cuando la respuesta proporcionada no es válida."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "¿Vendemos pantallas de bloque acústico?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns?"
msgstr "¿Tiene algún otro comentario, pregunta o duda?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr "¿Cree que nos faltan productos en nuestro catálogo? (sin puntos)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "Perros"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees?"
msgstr "¿A cuál familia de árboles pertenece el cornejo?"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q2
msgid "Don't be shy, be wild!"
msgstr "No se contenga, déjese llevar."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "Abeto de Douglas"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "Cajón"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Duplicate Question"
msgstr "Duplicar pregunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Edit Survey"
msgstr "Editar encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "Editar en el backend"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "Correo electrónico"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "End Live Session"
msgstr "Terminar sesión en vivo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "End Message"
msgstr "Mensaje final"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "Fecha y hora de finalización"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "End of Survey"
msgstr "Fin de la encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "Ingresar código de la sesión"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "Mensaje de error"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "Europa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "Tejo común"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Exclude Tests"
msgstr "Excluir pruebas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "Contacto existente"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "Correos electrónicos existentes"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Extremely likely"
msgstr "Muy probable"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (Islandia)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Failed"
msgstr "No aprobada"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Failed only"
msgstr "Solo no aprobadas"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Fanta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model:survey.survey,title:survey.survey_feedback
msgid "Feedback Form"
msgstr "Formulario de retroalimentación"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "Ficus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Filter surveys"
msgstr "Filtrar encuestas"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Final Leaderboard"
msgstr "Tabla de clasificación final"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug2
msgid "Fish"
msgstr "Pescado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome p. ej. fa-tasks"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_food_preferences
msgid "Food Preferences"
msgstr "Preferencias alimentarias"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "Texto libre"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "Respuesta de texto libre"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris)?"
msgstr "¿De qué continente es nativo el pino silvestre (pinus sylvestris)?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "Frutas"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "Frutas y verduras"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Functional Training"
msgstr "Formación funcional"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "Insignia de ludificación"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Desafío de ludificación"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Gather feedbacks from your employees and customers"
msgstr "Recopile retroalimentación de sus empleados y clientes"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "Geografía"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "Otorgar insignia"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "Escriba la lista de todos los tipos de madera que vendemos."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "Bueno"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Good luck!"
msgstr "¡Buena suerte!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "Buena relación calidad-precio"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "Got it!"
msgstr "¡Listo!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "Toronjas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "Gestionar existente"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Handle quiz &amp; certifications"
msgstr "Gestionar los cuestionarios y certificaciones"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "Dura"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
#: model:ir.model.fields,field_description:survey.field_survey_user_input__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__has_image_only_suggested_answer
msgid "Has image only suggested answer"
msgstr "La respuesta sugerida es de solo imagen"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "Altura"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Help Participants know what to write"
msgstr "Ayude a los participantes a saber qué escribir"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "Hemiunu"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Here, you can overview all the participations."
msgstr ""
"Aquí podrá observar una perspectiva general de todas las participaciones."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "Alta calidad"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "Historial"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online?"
msgstr "¿Con qué frecuencia compra productos en línea?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How frequently do you use our products?"
msgstr "¿Con qué frecuencia usa nuestros productos?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How good of a presenter are you? Let's find out!"
msgstr "¿Qué tan buen presentador es? ¡Averigüémoslo!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How likely are you to recommend the following products to a friend?"
msgstr ""
"¿Qué tan probable es que recomiende los siguientes productos a un amigo?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "¿Cuánto mide el río Nilo Blanco?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr ""
"¿Cuántas sillas cree que deberíamos intentar vender en un año? (sin puntos)"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "¿Cuántos días tiene nuestra garantía de devolución de dinero?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How many orders did you pass during the last 6 months?"
msgstr "¿Cuántos pedidos aprobó en los últimos 6 meses?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website?"
msgstr "¿Cuántas veces ha pedido productos en nuestro sitio web?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "¿Cuántas versiones del escritorio en L tenemos?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last?"
msgstr "¿Cuántos años duró la guerra de los 100 años?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "¿Cuánto vendemos nuestra caja de administración de cables?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "¿Con qué frecuencia se deben regar esas plantas?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you?"
msgstr "¿Cuántos años tiene?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"En realidad no me gusta pensar. Creo que la gente piensa que me gusta pensar"
" mucho. Y no me gusta. No me gusta pensar en absoluto."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr ""
"Me fascina el aire. Si quitas el aire del cielo, todos los pájaros caerían "
"al suelo. Y todos los aviones también."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "He agregado productos a mi lista de deseos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "¡No tengo ni idea, soy un perro!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young!"
msgstr "¡He notado la gravedad desde que era muy joven!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "Token de identificación"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr ""
"Si se selecciona Número, mostrará el número de preguntas respondidas sobre "
"el número total de preguntas para responder."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr ""
"Si un cliente compra una garantía de 1 año el 6 de enero de 2020, ¿cuándo "
"esperamos que expire la garantía?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr ""
"Si un cliente compra un producto el 6 de enero de 2020, ¿cuál es el último "
"día en que esperamos enviarlo?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr ""
"Si está marcada, esta opción guardará la respuesta del usuario como su "
"dirección de correo electrónico."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr ""
"Si está marcada, esta opción guardará la respuesta del usuario como su "
"apodo."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "Si está marcado, los usuarios pueden volver a las páginas anteriores."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr ""
"Si está marcado, los usuarios deben iniciar sesión antes de responder "
"incluso con un token válido."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "If other, please specify:"
msgstr "Si otra, por favor, precisar:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr ""
"Si selecciona aleatorio, agregue el número de preguntas aleatorias junto a "
"la sección."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr ""
"Si se selecciona aleatorio, puede configurar el número de preguntas "
"aleatorias por sección. Este modo se ignora en sesiones en vivo."

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_1_question_5
msgid "If yes, explain what you think is missing, give examples."
msgstr "En caso afirmativo, explique qué cree que falta, dé ejemplos."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Image"
msgstr "Imagen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image_filename
msgid "Image Filename"
msgstr "Nombre del archivo de la imagen"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Image Zoom Dialog"
msgstr "Diálogo de zum de la imagen"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr "Imhotep"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "Poco práctico"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "En proceso"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "En la siguiente lista, seleccione todas las coníferas."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop?"
msgstr "¿En qué país se desarrolló la técnica del bonsái?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr ""
"Incluya esta pregunta como parte de la puntuación del cuestionario. Requiere"
" que se tenga en cuenta una respuesta y una puntuación de respuesta."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Incorrect"
msgstr "Incorrecto"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "Ineficaz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "La entrada debe ser un correo electrónico"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/integer_update_flag_field/integer_update_flag_fields.js:0
msgid "Integer updating comparison flag"
msgstr "Entero para actualizar el marcador de comparación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "Token de invitación"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "Solo personas invitadas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "Es un editor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is a Certification"
msgstr "Es una certificación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "¿Es una pagina?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "Está en una sesión"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_placed_before_trigger
msgid "Is misplaced?"
msgstr "¿Está fuera de lugar?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is not a Certification"
msgstr "No es una certificación"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr "La respuesta del usuario forma parte de una sesión de encuesta o no."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft?"
msgstr "¿La madera de una conífera es dura o blanda?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_placed_before_trigger
msgid "Is this question placed before any of its trigger questions?"
msgstr ""
"¿Se antepone esta pregunta a cualquiera de sus preguntas desencadenantes?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "Estambul"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug3
msgid "It depends"
msgstr "Depende"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It does not mean anything specific"
msgstr "No tiene un significado en específico"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees focus on what you are saying"
msgstr "Ayuda a los asistentes a centrarse en lo que está presentando"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees remember the content of your presentation"
msgstr "Ayuda a los asistentes a recordar el contenido de su presentación"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is a small bit of text, displayed to help participants answer"
msgstr ""
"Es un pequeño texto que se muestra para ayudar a los participantes a "
"responder"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is an option that can be different for each Survey"
msgstr "Es una opción que puede ser diferente para cada encuesta"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "Es fácil encontrar el producto que quiero"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is more engaging for your audience"
msgstr "Es más interesante para su público"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It's a Belgian word for \"Management\""
msgstr "Es una palabra belga que significa \"Gestión\""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "Iznogoud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"Nunca he querido ir a Japón, simplemente porque no me gusta el pescado, y sé"
" que es muy popular allá en África."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "Japón"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "Unirse a la sesión"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "Just to categorize your answers, don't worry."
msgstr "Solo es para clasificar sus respuestas, no se preocupe."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "Kim Jong-hyun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "Kurt Cobain"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Label"
msgstr "Etiqueta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "Secuencia de orden de la etiqueta"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr "Etiquetas utilizadas para las opciones propuestas: filas de matriz"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr ""
"Etiquetas utilizadas para las opciones propuestas: opción simple, opción "
"múltiple y columnas de matriz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "Idioma"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "Escritorio grande"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "Última pregunta/página mostrada"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "Actividades retrasadas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Launch Session"
msgstr "Iniciar sesión"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "Tabla de clasificación"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Leave"
msgstr "Abandonar"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "Patas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "Limoneros"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's get started!"
msgstr "¡Empecemos!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's give it a spin!"
msgstr "¡Vamos a probarlo!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's have a look at your answers!"
msgstr "Revisemos sus respuestas"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's open the survey you just submitted."
msgstr "Abramos la encuesta que acaba de enviar."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Likely"
msgstr "Probable"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Limit Attempts"
msgstr "Limitar intentos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "Número limitado de intentos"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Live Session"
msgstr "Sesión en vivo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__session_available
msgid "Live Session available"
msgstr "Sesión en vivo disponible"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "Sesiones en vivo"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__live_session
msgid "Live session"
msgstr "Sesión en vivo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_available
msgid "Live session available"
msgstr "Sesión en vivo disponible"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Load a <b>sample Survey</b> to get started quickly."
msgstr "Suba una <b>encuesta de prueba</b> y empiece rápidamente."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "Se requiere iniciar sesión"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "Plantilla de correo electrónico"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "Respuesta obligatoria"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "Matriz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "Filas de matriz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "Tipo de matriz"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "¡La fecha máxima no puede ser menor que la mínima!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr ""
"¡La fecha y hora máxima no puede ser menor que la fecha y hora mínima!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "¡La longitud máxima no puede ser menor que la mínima!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "¡El valor máximo no puede ser menor que el mínimo!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Maximum"
msgstr "Máximo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "Fecha máxima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "Fecha y hora máxima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "Longitud máxima del texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_max_obtainable
msgid "Maximum obtainable score"
msgstr "Puntuación máxima obtenible"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "Valor máximo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "Quizá estaba buscando"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Min/Max Limits"
msgstr "Límites mínimos/máximos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Minimum"
msgstr "Mínimo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "Fecha mínima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "Fecha y hora mínima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "Longitud mínima de texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "Valor mínimo"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "Azul moderno"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "Oro moderno"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "Morado moderno"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "Alces"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "Monte Elbrus (Rusia)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "Monte Etna (Italia - Sicilia)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "Monte Teide (España - Tenerife)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "Pino negro"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "Cuadro de texto de múltiples líneas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "Opción múltiple con múltiples respuestas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "Opción múltiple con una respuesta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "Opción múltiple: se permiten varias respuestas"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "Opción múltiple: solo una respuesta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "Múltiples opciones por fila"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "Proveedor de MyCompañía"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "Certificación de proveedor de MyCompañía"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Neutral"
msgstr "Neutral"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Never (less than once a month)"
msgstr "Nunca (menos de una vez al mes)"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "Nuevo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "Nueva York"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "Nueva invitación"

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_survey_user_input_completed
msgid "New participation completed."
msgstr "Nueva participación completada."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next"
msgstr "Siguiente"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next Skipped"
msgstr "Siguiente omitida"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "Apodo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug2
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug2
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "No"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No Questions yet!"
msgstr "Todavía no hay preguntas"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "No Survey Found"
msgstr "No se encontraron encuestas"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "No answers yet!"
msgstr "Todavía no hay respuestas"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "No attempts left."
msgstr "No quedan intentos."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "Todavía no hay preguntas, regrese después."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "Sin puntuación"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "No se encontraron etiquetas de encuesta"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "No se encontraron líneas de entrada de usuario"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's too small for the human eye."
msgstr "No, es demasiado pequeño para el ojo humano."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "Pícea de Noruega"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "Ni bueno ni malo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Not likely at all"
msgstr "Nada probable"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now that you are done, submit your form."
msgstr "Ya que ha terminado, envíe su formulario."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now, use this shortcut to go back to the survey."
msgstr "Utilice este acceso rápido para regresar a la encuesta."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__scale
msgid "Number"
msgstr "Número"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "Número de intentos"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "Número de cajones"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "Valor numérico"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "Respuesta numérica"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Object-Directed Open Organization"
msgstr "Object-Directed Open Organization"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Occurrence"
msgstr "Incidencia"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Odoo"
msgstr "Odoo"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Odoo Certification"
msgstr "Certificación de Odoo"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "Silla de oficina negra"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Often (1-3 times per week)"
msgstr "A menudo (1-3 veces por semana)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"On Survey questions, one can define \"placeholders\". But what are they for?"
msgstr ""
"En las preguntas de Encuesta se pueden definir \"marcadores de posición\", "
"pero... ¿para qué sirven?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "Una vez al día"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "Una vez al mes"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "Una vez a la semana"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "Una vez al año"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "Una opción por fila"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to answer at least half the questions correctly"
msgstr "Hay que responder correctamente al menos la mitad de las preguntas"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to get 50% of the total score"
msgstr "Se debe obtener el 50 % de la puntuación total"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "Una página por pregunta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "Una página por sección"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "Una página con todas las preguntas"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Only a single question left!"
msgstr "¡Sólo queda una pregunta!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Only show survey results having selected this answer"
msgstr ""
"Solo mostrar los resultados de la encuesta si se seleccionó esta respuesta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Only survey users can manage sessions."
msgstr "Solo los usuarios de encuestas pueden administrar sesiones."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Oops! No survey matches this code."
msgstr "¡Uy! Ninguna encuesta coincide con este código."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid ""
"Oopsie! We could not let you open this survey. Make sure you are using the correct link and are allowed to\n"
"                        participate or get in touch with its organizer."
msgstr ""
"¡Uy! No tiene acceso a esta encuesta. Asegúrese de que está utilizando el enlace correcto y que puede\n"
"                        participar o póngase en contacto con el organizador."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Open Session Manager"
msgstr "Administrador de sesión abiertas"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/description_page_field.xml:0
msgid "Open section"
msgstr "Sección abierta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Optional previous answers required"
msgstr "Se requieren respuestas previas opcionales"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma de traducción opcional (código ISO) a seleccionar para el envío de "
"correos electrónicos. Si no se selecciona esta opción, se utilizará la "
"versión en inglés. Por lo general, se usa una expresión de marcador de "
"posición para indicar el idioma adecuado, por ejemplo, {{ "
"object.partner_id.lang }}."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Options"
msgstr "Opciones"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Organizational Development for Operation Officers"
msgstr "Organizational Development for Operation Officers"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Other (see comments)"
msgstr "Otro (ver comentarios)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "Nuestra empresa en unas pocas preguntas ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "Servidor de correo de salida"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Overall Performance"
msgstr "Rendimiento global"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "Demasiado caro"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "Página"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "Páginas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Pagination"
msgstr "Paginación"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "Papiro"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Partially"
msgstr "Parcialmente"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Participant"
msgstr "Participante"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participants"
msgstr "Participantes"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Participate to %(survey_name)s"
msgstr "Participar en %(survey_name)s"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "Participar en {{ object.survey_id.display_name }}"

#. module: survey
#: model:mail.message.subtype,name:survey.mt_survey_survey_user_input_completed
#: model:mail.message.subtype,name:survey.mt_survey_user_input_completed
msgid "Participation completed"
msgstr "Participación completada"

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_user_input_completed
msgid "Participation completed."
msgstr "Participación completada."

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participations"
msgstr "Participaciones"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "Contacto"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Passed"
msgstr "Aprobado"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed and Failed"
msgstr "Aprobadas y no aprobadas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed only"
msgstr "Solo aprobadas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr ""
"Preste atención a la pantalla del anfitrión hasta la siguiente pregunta."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage left"
msgstr "Porcentaje restante"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Performance by Section"
msgstr "Rendimiento por sección"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "Quizás"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "Peter W. Higgs"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Badge..."
msgstr "Elija una insignia..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Style..."
msgstr "Elija un estilo..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Template..."
msgstr "Elija una plantilla..."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "Elija un asunto"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_ids
msgid ""
"Picking any of these answers will trigger this question.\n"
"Leave the field empty if the question should always be displayed."
msgstr ""
"Si elige cualquiera de estas respuestas, aparecerá esta pregunta.\n"
"Deje el campo vacío si siempre se debe mostrar la pregunta."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "Pinaceae"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_placeholder
msgid "Placeholder"
msgstr "Marcador de posición"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Please complete this very short survey to let us know how satisfied your are"
" with our products."
msgstr ""
"Complete esta breve encuesta para hacernos saber qué tan satisfecho está con"
" nuestros productos."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Please enter at least one valid recipient."
msgstr "Ingrese al menos un destinatario válido."

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_food_preferences
msgid "Please give us your preferences for this event's dinner!"
msgstr "Indíquenos sus preferencias para la cena de este evento."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"Asegúrese de tener al menos una pregunta en su encuesta. También necesita al"
" menos una sección si elige el diseño \"Página por sección\".<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "Políticas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "Pomelos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "Mala calidad"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Practice in front of a mirror"
msgstr "Practique frente al espejo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "Preguntas predefinidas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Preview"
msgstr "Vista previa"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "Precios"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "Imprimir"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Print Results"
msgstr "Imprimir resultados"

#. module: survey
#: model:ir.actions.server,name:survey.action_survey_print
msgid "Print Survey"
msgstr "Imprimir encuesta"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "Productos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Progress bar"
msgstr "Barra de progreso"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "Pregunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Question & Pages"
msgstr "Preguntas y páginas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "Pregunta (como fila de matriz)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_form
msgid "Question Answer Form"
msgstr "Formulario de preguntas y respuestas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "Número de preguntas y respuestas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Question Selection"
msgstr "Selección de preguntas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "Límite de tiempo de preguntas alcanzado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_type
msgid "Question Type"
msgstr "Tipo de pregunta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Question type should be empty for these pages: %s"
msgstr "El tipo de pregunta debe estar vacío para estas páginas: %s"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Questions"
msgstr "Preguntas"

#. module: survey
#: model:ir.ui.menu,name:survey.survey_menu_questions
msgid "Questions & Answers"
msgstr "Preguntas & respuestas"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_ids
msgid ""
"Questions containing the triggering answer(s) to display the current "
"question."
msgstr ""
"Preguntas que contienen la(s) respuesta(s) desencadenante(s) para mostrar la"
" pregunta actual."

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "Cuestionario sobre nuestra empresa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "Cuestionario aprobado"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "Cuestionario aprobado"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/radio_selection_with_filter/radio_selection_field_with_filter.js:0
msgid "Radio for Selection With Filter"
msgstr "Radio para selección con filtro"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per Section"
msgstr "Aleatorio por sección"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Rarely (1-3 times per month)"
msgstr "Casi nunca (1-3 veces por mes)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__rating_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "Listo"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Ready to change the way you <b>gather data</b>?"
msgstr "¿Está listo para cambiar su forma de <b>recopilar datos</b>?"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Ready to test? Pick a sample or create one from scratch..."
msgstr "¿Listo para probarlo? Elija un ejemplo o cree uno desde cero..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "Destinatarios"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Red Pen"
msgstr "Bolígrafo rojo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Registered"
msgstr "Registrado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "Modelo de visualización"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Reopen"
msgstr "Reabrir"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Require Login"
msgstr "Es necesario iniciar sesión"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Required Score (%)"
msgstr "Puntuación obligatoria (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "Reenviar comentario"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "Reenviar invitación"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "Reenviar invitación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Responsible"
msgstr "Responsable"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__restrict_user_ids
msgid "Restricted to"
msgstr "Restringido a"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "Volver a intentar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Review your answers"
msgstr "Revise sus respuestas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "Recompensa para respuestas rápidas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "Recompensas para desafíos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "Respuesta en fila"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Fila1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Fila2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Fila3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Fila4"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de envío del SMS"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "Salicaceae"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "Guardar como correo electrónico de usuario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "Guardar como apodo de usuario"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__scale
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale"
msgstr "Balanza"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max_label
msgid "Scale Maximum Label"
msgstr "Etiqueta del valor máximo de la escala"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max
msgid "Scale Maximum Value"
msgstr "Valor máximo de la escala"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Maximum Value (0 to 10)"
msgstr "Valor máximo de la escala (de 0 a 10)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_mid_label
msgid "Scale Middle Label"
msgstr "Etiqueta del valor medio de la escala"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min_label
msgid "Scale Minimum Label"
msgstr "Etiqueta del valor mínimo de la escala"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min
msgid "Scale Minimum Value"
msgstr "Valor mínimo de la escala"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Minimum Value (0 to 10)"
msgstr "Valor mínimo de la escala (0 a 10)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_scale
msgid "Scale value"
msgstr "Valor de la escala"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "Ciencias"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Score"
msgstr "Puntuación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "Puntuación (%)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "Valor de puntuación para una respuesta correcta a esta pregunta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "Con puntuación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
msgid "Scoring"
msgstr "Puntuación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__scoring_type
msgid "Scoring Type"
msgstr "Tipo de puntuación"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers_after_page
msgid "Scoring with answers after each page"
msgstr "Puntuación con respuestas después de cada página"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "Puntuación con respuestas al final"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers"
msgstr "Puntuación sin respuestas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "Buscar etiqueta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Buscar pregunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey User Inputs"
msgstr "Buscar entradas de usuario de la encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "Buscar líneas de entrada del usuario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "Sección"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "Secciones y preguntas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "See results"
msgstr "Ver resultados"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "See you soon!"
msgstr "¡Nos vemos pronto!"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr ""
"Seleccione todas las personalizaciones disponibles para nuestro escritorio "
"personalizable"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "Seleccione todos los productos existentes"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "Seleccione todos los productos que se venden por 100 € o más"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "Seleccione árboles que hayan realizado más de 20K ventas este año"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "Enviar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__send_email
msgid "Send Email"
msgstr "Enviar correo electrónico"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send by Email"
msgstr "Enviar por correo electrónico"

#. module: survey
#: model:mail.template,description:survey.mail_template_certification
msgid "Sent to participant if they succeeded the certification"
msgstr "Enviado al participante si ha aprobado la certificación"

#. module: survey
#: model:mail.template,description:survey.mail_template_user_input_invite
msgid "Sent to participant when you share a survey"
msgstr "Enviado al participante cuando comparte una encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
msgid "Session Code"
msgstr "Código de sesión"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
msgid "Session Link"
msgstr "Enlace de sesión"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "Estado de sesión"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "El código de sesión debe ser único"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "Shanghái"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Share"
msgstr "Compartir"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Share a Survey"
msgstr "Compartir una encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_table_pagination
msgid "Show All"
msgstr "Mostrar todo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "Mostrar campo de comentarios"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Correct Answer(s)"
msgstr "Mostrar respuestas correctas"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Final Leaderboard"
msgstr "Mostrar tabla de clasificación final "

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Leaderboard"
msgstr "Mostrar tabla de clasificación"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Results"
msgstr "Mostrar resultados"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "Mostrar tabla de clasificación de la sesión"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Show them slides with a ton of text they need to read fast"
msgstr ""
"Mostrar diapositivas con una gran cantidad de texto que deben leer "
"rápidamente"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "Cuadro de texto de una línea"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Skipped"
msgstr "Omitida"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "Blanda"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Some emails you just entered are incorrect: %s"
msgstr "Algunos correos electrónicos que acaba de ingresar son incorrectos:%s"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"Some general information about you. It will be used internally for "
"statistics only."
msgstr ""
"Información general sobre usted. Se utilizará internamente sólo para "
"estadísticas."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2
msgid "Some questions about our company. Do you really know us?"
msgstr "Algunas preguntas sobre nuestra empresa. ¿Realmente nos conoce? "

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Someone just participated in \"%(survey_title)s\"."
msgstr "Alguien acaba de participar en \"%(survey_title)s\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "Lo sentimos, nadie ha respondido esta encuesta todavía."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "Lo sentimos, no ha sido lo suficientemente rápido."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "América del Sur"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "Corea del Sur"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "Estaciones espaciales"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak softly so that they need to focus to hear you"
msgstr "Hablar claro para facilitar la concentración"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak too fast"
msgstr "Hablar demasiado rápido"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "Primavera"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "Iniciar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "Iniciar certificación"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Start Live Session"
msgstr "Iniciar sesión en vivo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "Iniciar encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "Fecha y hora de inicio"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "Estado"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya ha pasado\n"
"Hoy: la fecha límite es hoy\n"
"Planificada: actividades futuras."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug1
msgid "Steak with french fries"
msgstr "Bistec con patatas fritas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "Fresas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "Asunto"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "Asunto..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "Enviar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "Éxito"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio (%)"
msgstr "Porcentaje de éxito (%)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate"
msgstr "Tasa de éxito"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "Valores sugeridos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "Respuesta sugerida"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_answer_value_not_empty
msgid ""
"Suggested answer value must not be empty (a text and/or an image must be "
"provided)."
msgstr ""
"El valor de respuesta sugerido no se debe estar vacío (debe proporcionarse "
"un texto o una imagen)."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "Valor sugerido"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "Sugerencia"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "Verano"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_activity
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "Encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Survey Access Error"
msgstr "Error de acceso a la encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "Línea de respuesta de encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_first_submitted
msgid "Survey First Submitted"
msgstr "Primera encuesta enviada"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "ID de encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Asistente de invitación a encuestas"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "Etiqueta de la encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Survey Link"
msgstr "Enlace de la encuesta "

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Survey Participant"
msgstr "Participante de la encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Pregunta de la encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Survey Time Limit"
msgstr "Límite de tiempo de la encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "Límite de tiempo de la encuesta alcanzado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "Título de la encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__survey_type
msgid "Survey Type"
msgstr "Tipo de encuesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "URL de encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Entrada de usuario de la encuesta"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "Línea de entrada de usuario de la encuesta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "Entradas de usuario de la encuesta"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Certification Success"
msgstr "Encuesta: éxito de la certificación"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "Encuesta: invitación"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Encuestas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Takaaki Kajita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Take Again"
msgstr "Volver a hacer"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Test"
msgstr "Prueba"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Test Entry"
msgstr "Entrada de prueba"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_3
msgid "Test your knowledge of our policies."
msgstr "¡Ponga a prueba su conocimiento sobre nuestras políticas!"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_2
msgid "Test your knowledge of our prices."
msgstr "¡Ponga a prueba su conocimiento sobre nuestros precios!"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_1
msgid "Test your knowledge of your products!"
msgstr "¡Ponga a prueba su conocimiento sobre sus productos!"

#. module: survey
#: model_terms:survey.survey,description:survey.vendor_certification
msgid "Test your vendor skills!"
msgstr "¡Ponga a prueba sus habilidades como vendedor!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Tests Only"
msgstr "Solo pruebas"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "Texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "Respuesta de texto"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you for your participation, hope you had a blast!"
msgstr "Gracias por su participación, esperamos que la haya pasado bien."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you very much for your feedback. We highly value your opinion!"
msgstr "Muchas gracias por su retroalimentación. ¡Valoramos mucho su opinión!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Thank you!"
msgstr "¡Gracias!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you. We will contact you soon."
msgstr "Gracias, nos comunicaremos con usted a la brevedad."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"The access of the following surveys is restricted. Make sure their responsible still has access to it: \n"
"%(survey_names)s\n"
msgstr ""
"El acceso a las siguientes encuestas es restringido. Asegúrese de que el responsable de las encuestas tenga acceso a ellas: \n"
"%(survey_names)s\n"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "The answer must be in the right type"
msgstr "La respuesta debe estar en el tipo adecuado"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
msgid "The answer you entered is not valid."
msgstr "La respuesta introducida no es válida."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr ""
"El límite de intentos debe ser un número positivo si la encuesta tiene un "
"número limitado de intentos."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "¡La insignia de cada encuesta debe ser única!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "El proceso de pago es claro y seguro"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "The correct answer was:"
msgstr "La respuesta correcta era:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "La pregunta actual de la sesión de la encuesta."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr ""
"La descripción aparecerá en la página de inicio de la encuesta. Puede usar "
"esto para proporcionarle el propósito y las reglas a sus candidatos antes de"
" que comiencen."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following customers have already received an invite"
msgstr "Los siguientes clientes ya han recibido una invitación"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following emails have already received an invite"
msgstr "Los siguientes correos electrónicos ya recibieron una invitación"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""
"Los siguientes destinatarios no tienen cuenta de usuario: %s. Debe crear "
"cuentas de usuario para ellos o permitir el registro externo en la "
"configuración."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "La nueva distribución y el diseño son frescos y actualizados"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "No está autorizado a ver la página que está buscando."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "El porcentaje de éxito debe definirse entre 0 y 100."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "La pregunta es limitada en el tiempo"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scale
msgid ""
"The scale must be a growing non-empty range between 0 and 10 (inclusive)"
msgstr ""
"La escala debe ser un rango creciente no vacío ente 0 y 10 (incluyéndolo)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "The session did not start yet."
msgstr "La sesión aún no ha comenzado."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "La sesión comenzará automáticamente cuando se inicie el anfitrión."

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The survey has already started."
msgstr "La encuesta ya ha comenzado."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "La encuesta tiene tiempo limitado"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr ""
"La hora a la que ha comenzado la pregunta actual, utilizada para controlar "
"el temporizador para los asistentes."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr ""
"El límite de tiempo debe ser un número positivo si la encuesta tiene un "
"límite de tiempo."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr ""
"La herramienta para comparar los productos es útil para tomar una decisión"

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The user has not succeeded the certification"
msgstr "El usuario no ha aprobado la certificación"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "Ocurrió un error al validar la encuesta."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "They are a default answer, used if the participant skips the question"
msgstr ""
"Hay una respuesta por defecto que se utiliza si el participante omite la "
"pregunta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"They are technical parameters that guarantees the responsiveness of the page"
msgstr ""
"Son parámetros técnicos que garantizan la capacidad de respuesta de la "
"página"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "This answer cannot be overwritten."
msgstr "Esta respuesta no se puede sobrescribir."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This answer must be an email address"
msgstr "Esta respuesta debe ser una dirección de correo electrónico"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "This answer must be an email address."
msgstr "Esta respuesta debe ser una dirección de correo electrónico."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr ""
"Los asistentes utilizarán este código para acceder a la sesión. Puede "
"modificarlo a su gusto."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a Test Survey Entry."
msgstr "Esta es una entrada de prueba de una encuesta."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a date"
msgstr "Esto no es una fecha"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a number"
msgstr "Esto no es un número"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "Este mensaje será mostrado cuando se complete la encuesta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "This question requires an answer."
msgstr "Esta pregunta requiere una respuesta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "This question was skipped"
msgstr "Esta pregunta fue omitida"

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p1
msgid ""
"This section is about general information about you. Answering them helps "
"qualifying your answers."
msgstr ""
"Esta sección trata de su información general. Contestar ayuda a cualificar "
"sus respuestas. "

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p2
msgid "This section is about our eCommerce experience itself."
msgstr ""
"Esta sección trata sobre nuestra experiencia propia en el comercio "
"electrónico. "

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_quiz
msgid ""
"This small quiz will test your knowledge about our Company. Be prepared!"
msgstr ""
"Este pequeño cuestionario pondrá a prueba sus conocimientos sobre nuestra "
"empresa. ¡Prepárese!"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_feedback
msgid ""
"This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience."
msgstr ""
"Esta encuesta le permite dar su opinión sobre su experiencia con nuestros productos .\n"
"    Responderlo nos ayuda a mejorar su experiencia. "

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"Esta encuesta no permite la participación de personas externas. Debe crear "
"cuentas de usuario o actualizar el modo de acceso a la encuesta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest!"
msgstr "Esta encuesta se ha cerrado. ¡Gracias por su interés!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr "Solo las personas registradas pueden participar en esta encuesta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time & Scoring"
msgstr "Tiempo y puntuación"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time Limit"
msgstr "Tiempo límite"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time Limit (seconds)"
msgstr "Límite de tiempo (segundos)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "Límite de tiempo (minutos)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Time limit (seconds)"
msgstr "Límite de tiempo (segundos)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this certification:"
msgstr "Límite de tiempo para esta certificación:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this survey:"
msgstr "Límite de tiempo para esta encuesta:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time limits are only available for Live Sessions."
msgstr "Los límites solo están disponibles para las sesiones en vivo."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "Título"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "To join:"
msgstr "Para unirse:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr ""
"Para responder esta encuesta, por favor cierre las otras pestañas  <strong "
"class=\"text-danger\"/>."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "Tokio"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Top User Responses"
msgstr "Mejores respuestas del usuario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "Puntuación total"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "Totalmente de acuerdo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "Totalmente en desacuerdo"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "Árboles"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_ids
msgid "Triggering Answers"
msgstr "Respuestas desencadenantes"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_ids
msgid "Triggering Questions"
msgstr "Preguntas desencadenantes"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid ""
"Triggers based on the following questions will not work because they are positioned after this question:\n"
"\"%s\"."
msgstr ""
"Los desencadenantes que se basan en las siguientes preguntas no funcionarán porque se sitúan después de esta pregunta:\n"
"\"%s\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Tipo"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción en el registro."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "Tipos de respuestas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "Ulmaceae"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"No se puede publicar el mensaje, configure la dirección de correo "
"electrónico del remitente."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Unanswered"
msgstr "Sin respuesta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Uncategorized"
msgstr "Sin categoría"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "Barato"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "Desafortunadamente, no pasó la prueba."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "Único"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Unlikely"
msgstr "Poco probable"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "Próximas actividades"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use a fun visual support, like a live presentation"
msgstr "Utilizar un soporte visual divertido, como una presentación en vivo"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use humor and make jokes"
msgstr "Utilizar el humor y hacer bromas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Use template"
msgstr "Usar plantilla"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Use the breadcrumbs to quickly go back to the dashboard."
msgstr "Utilice las migas de pan para volver rápidamente al tablero."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr ""
"Utilice este campo para agregar explicaciones adicionales sobre su pregunta "
"o para ilustrarla con imágenes o un vídeo"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr ""
"Se usa en secciones aleatorias para tomar X preguntas aleatorias de todas "
"las preguntas de esa sección."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "Útil"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "Usuario"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "Elección del usuario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr "Entrada del usuario"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "Respuestas del usuario"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "Detalles de la línea de entrada del usuario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "Respuestas del usuario"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "Los usuarios pueden volver atrás"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "Los usuarios pueden registrarse"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "Validar entrada"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/question_page_one2many_field.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error"
msgstr "Error de validación"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_label
msgid "Value Label"
msgstr "Etiqueta de valor"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "Verduras"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug2
msgid "Vegetarian burger"
msgstr "Hamburguesa vegetariana"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug1
msgid "Vegetarian pizza"
msgstr "Pizza vegetariana"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "Muy baratos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "Vietnam"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "Waiting for attendees..."
msgstr "En espera de los asistentes..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr ""
"Hemos registrado su respuesta. Espere a que el anfitrión pase a la siguiente"
" pregunta."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"We like to say that the apple doesn't fall far from the tree, so here are "
"trees."
msgstr ""
"Nos gusta decir que la manzana no cae lejos del árbol, así que aquí hay "
"árboles. "

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5
msgid "We may be interested by your input."
msgstr "Nos interesa su opinión."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,help:survey.field_survey_user_input__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Welcome to this Odoo certification. You will receive 2 random questions out "
"of a pool of 3."
msgstr ""
"Bienvenido a esta certificación de Odoo. Recibirá 2 preguntas al azar de un "
"conjunto de 3."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr ""
"¿Qué día y hora cree que es más probable que aumenten las llamadas para el "
"servicio de atención al cliente? (sin puntos)"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr ""
"¿Qué día cree que es mejor para realizar una venta anual? (sin puntos)"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce?"
msgstr "¿Qué opina de nuestro comercio electrónico?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "¿Qué opina de nuestros precios? (sin puntos)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey?"
msgstr "¿Qué opina de esta encuesta? "

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does \"ODOO\" stand for?"
msgstr "¿Qué significa \"odoo\"?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does one need to get to pass an Odoo Survey?"
msgstr "¿Qué se necesita para aprobar una encuesta de Odoo?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is a frequent mistake public speakers do?"
msgstr ""
"¿Cuál es un error frecuente que cometen los presentadores al hablar en "
"público?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is the best way to catch the attention of an audience?"
msgstr "¿Cuál es la mejor manera de captar la atención del público?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world?"
msgstr "¿Cuál es la ciudad más grande del mundo? "

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email?"
msgstr "¿Cuál es su correo electrónico? "

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname?"
msgstr "¿Cúal es su apodo?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239?"
msgstr "¿Cuál es, aproximadamente, la masa crítica del plutonio-239? "

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die?"
msgstr "¿Cuándo murió Genghis Khan?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree?"
msgstr ""
"¿Cuál es la fecha y hora exactas en las que Marc Demo cultivó su primer "
"manzano?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "¿Cuándo cosecha esas frutas?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born?"
msgstr "¿Cuándo nació Mitchell Admin?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth?"
msgstr "¿Cuál es su fecha de nacimiento?"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Whenever you pick an answer, Odoo saves it for you."
msgstr "Cada vez que elija una respuesta, Odoo la guardará."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from?"
msgstr "¿De dónde es?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live?"
msgstr "¿Dónde vive?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr ""
"Si queremos o no mostrar la tabla de clasificación de asistentes para esta "
"encuesta."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club?"
msgstr "¿Qué músico no está en el Club de los 27?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "¿A qué categoría pertenece un tomate?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe?"
msgstr "¿Cuál es el volcán más alto de Europa?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products?"
msgstr ""
"¿Cuál de las siguientes palabras usaría para describir nuestros productos?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr "¿Cuál de las siguientes opciones usaría para polinizar?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso?"
msgstr "¿Qué pintura/dibujo no es obra de Pablo Picasso?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "¿Qué cita es de Jean-Claude Van Damme?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you?"
msgstr "¿Quién es usted?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza?"
msgstr "¿Quién es el arquitecto de la Gran Pirámide de Giza?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass?"
msgstr ""
"¿Quién recibió un premio Nobel de Física por el descubrimiento de las "
"oscilaciones de neutrinos, lo que demuestra que los neutrinos tienen masa?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Why should you consider making your presentation more fun with a small quiz?"
msgstr ""
"¿Por qué debería considerar hacer su presentación más divertida con la ayuda"
" de una pequeña encuesta?"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "Ancho"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "Willard S. Boyle"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "Invierno"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q2
msgid "Would you prefer a veggie meal if possible?"
msgstr "¿Prefiere una comida vegetariana si es posible?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"AAAA-MM-DD\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"AAAA-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Yellow Pen"
msgstr "Bolígrafo amarillo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug1
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug1
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "Sí"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "Sí, eso es lo único que puede ver un ojo humano."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr ""
"Solo puede crear certificaciones para encuestas que tengan un mecanismo de "
"puntuación."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid ""
"You can share your links through different means: email, invite shortcut, "
"live presentation, ..."
msgstr ""
"Puede compartir sus enlaces a través de diferentes medios: correo "
"electrónico, acceso rápido desde la invitación, presentación en vivo, ..."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid ""
"You cannot delete questions from surveys \"%(survey_names)s\" while live "
"sessions are in progress."
msgstr ""
"No puede eliminar preguntas de las encuestas \"%(survey_names)s\" mientras "
"haya sesiones en vivo en curso."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr ""
"No puede enviar una invitación para una encuesta de \"Una página por "
"sección\" si la encuesta no tiene secciones."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr ""
"No puede enviar una invitación para una encuesta de \"Una página por "
"sección\" si las secciones de la encuesta están vacías."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send an invitation for a survey that has no questions."
msgstr ""
"No puede enviar invitaciones para una encuesta que no tiene preguntas."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send invitations for closed surveys."
msgstr "No puede enviar invitaciones para una encuesta cerrada."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "Recibió la insignia"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "Ha obtenido"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "Su emoción"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Your responses will help us improve our product range to serve you even "
"better."
msgstr ""
"Sus respuestas nos ayudarán a mejorar nuestra gama de productos para "
"proporcionarle un mejor servicio."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom in"
msgstr "Acercar"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom out"
msgstr "Alejarse"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoomed Image"
msgstr "Imagen ampliada"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "[Question Title]"
msgstr "[Título de la pregunta]"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "res"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "respondido"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "attempts"
msgstr "intentos"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "close"
msgstr "cerrar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"e.g.  'Rick Sanchez' <<EMAIL>>, <EMAIL>"
msgstr ""
"p. ej. 'Rick Sanchez' <<EMAIL>>, <EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"Thank you very much for your feedback!\""
msgstr "p. ej. \"¡Le agradecemos su retroalimentación!\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"The following Survey will help us...\""
msgstr "p. ej., \"La siguiente encuesta nos ayudará...\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. \"What is the...\""
msgstr "p. ej., \"¿Qué es...?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "e.g. 4812"
msgstr "p. ej., 4812"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. Guidelines, instructions, picture, ... to help attendees answer"
msgstr ""
"p. ej. reglas, instrucciones, imágenes u otros elementos para ayudar a los "
"asistentes a responder"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "p. ej. Nadie puede resolver desafíos como usted"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "p. ej. Solucionador de problemas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. Satisfaction Survey"
msgstr "p. ej. Encuesta de satisfacción"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "iniciar sesión"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "minutes"
msgstr "minutos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "de"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "of achievement"
msgstr "de logros"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press CTRL+Enter"
msgstr "o pulsar CTRL+Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press Enter"
msgstr "o pulsar Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press ⌘+Enter"
msgstr "o pulsar ⌘+Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "páginas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "encuesta expirada"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "la encuesta está vacía"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "esta página"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "to"
msgstr "a"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"⚠️ This question is positioned before some or all of its triggers and could "
"be skipped."
msgstr ""
"⚠️ Esta pregunta se sitúa antes de algunos o todos sus desencadenantes y se "
"puede omitir."
