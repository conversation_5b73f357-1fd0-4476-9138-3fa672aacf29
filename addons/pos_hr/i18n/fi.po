# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_hr_multi_employee_sales_report
msgid "A collection of single session reports. One for each employee"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.single_employee_sales_report
msgid "Abigal Peterson"
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/product_screen/order_summary/order_summary.js:0
msgid "Access Denied"
msgstr "Käyttö estetty"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_daily_sales_reports_wizard__add_report_per_employee
msgid "Add a report per each employee"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Advanced rights"
msgstr "Edistyneet oikeudet"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "All Employees"
msgstr ""

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Pankkitiliotteen rivi"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Basic rights"
msgstr "Perusoikeudet"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.view_pos_daily_sales_reports_wizard
msgid ""
"Can't generate a report per employee! as selected session has no orders "
"associated with any employee."
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Cash in/out"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_pos_payment__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_pos_session__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "Myyjä"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/pos_order.py:0
msgid "Cashier %s"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
msgid "Cashier name"
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "Change Cashier"
msgstr "Vaihda myyjää"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/pos_session.py:0
msgid "Closed Register"
msgstr ""

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Counted"
msgstr "Laskettu"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Difference"
msgstr "Ero"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/login_screen/login_screen.js:0
msgid "Discard"
msgstr "Hylkää"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_account_bank_statement_line__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_pos_daily_sales_reports_wizard__employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "Työntekijä"

#. module: pos_hr
#: model:ir.actions.report,name:pos_hr.multi_employee_sales_report_action
msgid "Employee Sales Details"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.single_employee_sales_report
msgid "Employee Sales Report"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.single_employee_sales_report
msgid "Employee:"
msgstr "Työntekijä:"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
msgid "Employee: %(employee)s - PoS Config(s): %(config_list)s \n"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "Employees with basic access"
msgstr "Työntekijät, joilla on perusoikeudet"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "Employees with manager access"
msgstr "Työntekijät, joilla on esimiehen oikeudet"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/login_screen/login_screen.xml:0
msgid "Enter your PIN"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "If left empty, all employees can log in to PoS"
msgstr "Jos tyhjä, kaikki työntekijät voivat kirjautua kassaan"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "If left empty, only Odoo users have extended rights in PoS"
msgstr "Jos tyhjä, vain Odoo-käyttäjillä on laajennetut oikeudet kassassa"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/navbar/navbar.xml:0
msgid "Lock"
msgstr "Lukitse"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "No Cashiers"
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/login_screen/login_screen.xml:0
msgid "Open Register"
msgstr ""

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/pos_session.py:0
msgid "Opened register"
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Opening"
msgstr "Avataan"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "PIN not found"
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "Password?"
msgstr "Salasana?"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Payments"
msgstr "Maksut"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.js:0
msgid "Payments in %(paymentMethod)s"
msgstr ""

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Kassan asetukset"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr "Kassan päivittäinen raportti"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "Kassan tilaukset"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Kassan tilausten raportti"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Kassan maksut"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_session
msgid "Point of Sale Session"
msgstr "Kassan istunto"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/print_report_button/print_report_button.xml:0
msgid "Print"
msgstr "Tulosta"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_product_product
msgid "Product Variant"
msgstr "Tuotevariaatio"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Select Employee(s)"
msgstr ""

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_hr_single_employee_sales_report
msgid "Session sales details for a single employee"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_session__employee_id
msgid "The employee who currently uses the cash register"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_account_bank_statement_line__employee_id
msgid "The employee who made the cash move."
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,help:pos_hr.field_pos_payment__employee_id
msgid "The employee who uses the cash register."
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "There is no cashier available."
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/login_screen/login_screen.xml:0
msgid "Unlock Register"
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "Wrong PIN"
msgstr "Väärä PIN-koodi"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/product_screen/order_summary/order_summary.js:0
msgid "You are not allowed to change the price of a product."
msgstr ""

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr ""
"Et voi poistaa työntekijää, jota voidaan käyttää aktiivisessa "
"kassaistunnossa, sulje istunnot ensin:\n"
