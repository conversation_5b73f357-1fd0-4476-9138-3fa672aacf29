# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-12 10:37+0000\n"
"PO-Revision-Date: 2024-01-30 15:14+0400\n"
"Last-Translator: \n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Не найдено ни одной вехи. Давайте создадим одну!\n"
"                </p><p>\n"
"                    Отслеживайте основные моменты, которые необходимо достичь для достижения успеха.\n"
"                </p>\n"
"            "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "%(name)s's Sales Order Items"
msgstr "пункты заказа на продажу %(name)s"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "%(name)s's Sales Orders"
msgstr "заказы на продажу %(name)s"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales order associated with this project has been canceled. We recommend either updating the sales order item or canceling this project in alignment with the cancellation of the sales order.\" invisible=\"sale_order_state != 'cancel'\"/>"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales order associated with this task has been canceled. We recommend either updating the sales order item or canceling this task in alignment with the cancellation of the sales order.\" invisible=\"sale_order_state != 'cancel'\"/>"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "<span class=\"fa fa-lg fa-building-o fa-fw\" title=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr "<span class=\"fa fa-lg fa-building-o fa-fw\" title=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> Sales Order\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Make Billable\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                           <span class=\"o_stat_value\">0</span> Заказ на продажу\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Выставить счет\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Sales Orders\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Заказы на продажу\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">Заказ на продажу</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
msgid "<span>)</span>"
msgstr "<span>)</span>"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"В соответствии с конфигурацией продукта, количество поставляемого товара может быть автоматически рассчитано механизмом:\n"
"  - Ручной: количество задается вручную на линии\n"
"  - Аналитический из затрат: количество - это сумма количества из размещенных затрат\n"
"  - Из таймшетов: количество - это сумма часов, записанных в заданиях, связанных с данной линией продаж\n"
"  - Движение запасов: количество берется из подтвержденной выборки\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Based on Delivered Quantity (Manual)"
msgstr "На основе поставленного количества (вручную)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Based on Milestones"
msgstr "На основе вех"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_project__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_task__allow_billable
msgid "Billable"
msgstr "Оплачиваемый"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Cancelled"
msgstr "Отменен"

#. module: sale_project
#: model:ir.model,name:sale_project.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: sale_project
#: model:ir.actions.server,name:sale_project.model_sale_order_action_create_project
msgid "Create Project"
msgstr "Создать заказ"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Создать по заказу"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__project_partner_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__partner_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__partner_id
msgid "Customer"
msgstr "Клиент"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "Customer Invoices"
msgstr "Счета-фактуры для клиентов"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Delivered"
msgstr "Доставлено"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_project_only_product_template
msgid "Digital Marketing Campaign (project)"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__display_sale_order_button
msgid "Display Sales Order"
msgstr "Отображение заказа на продажу"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__display_sales_stat_buttons
msgid "Display Sales Stat Buttons"
msgstr "Кнопки отображения статистики продаж"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "Проект дисплея"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Done"
msgstr "Готово"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "Down Payments"
msgstr "Авансовые платежи"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_flooring_product_template
msgid "Flooring Services"
msgstr ""

#. module: sale_project
#: model:product.template,name:sale_project.product_product_furniture_product_template
msgid "Furniture Assembly"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "Созданный проект"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "Созданная задача"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr "Имеет СЦ для выставления счета-фактуры"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_with_nothing_to_invoice
msgid "Has a SO with an invoice status of No"
msgstr "Имеет СЦ со статусом счета-фактуры Нет"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "In Progress"
msgstr "В процессе"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__invoice_count
msgid "Invoice Count"
msgstr "Количество Счетов"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "Выставляйте счета за заказанные объемы, как только эта услуга будет продана."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice ordered quantities as soon as this service is sold. Create a project for the order with a task for each sales order line to track the time spent."
msgstr "Выставляйте счета за заказанные объемы, как только услуга будет продана. Создайте проект для этого заказа с задачей для каждой строки заказа на продажу, чтобы отслеживать потраченное время."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice ordered quantities as soon as this service is sold. Create a task in an existing project to track the time spent."
msgstr "Выставляйте счета за заказанные объемы, как только услуга будет продана. Создайте задачу в существующем проекте, чтобы отслеживать потраченное время."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice ordered quantities as soon as this service is sold. Create an empty project for the order to track the time spent."
msgstr "Выставляйте счета за заказанные объемы, как только услуга будет продана. Создайте пустой проект для этого заказа, чтобы отслеживать потраченное время."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice this service when it is delivered (set the quantity by hand on your sales order lines). "
msgstr "Выставляйте счет-фактуру при доставке услуги (количество указывайте вручную в строках заказа на продажу)."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice this service when it is delivered (set the quantity by hand on your sales order lines). Create a project for the order with a task for each sales order line to track the time spent."
msgstr "Выставляйте счет-фактуру по факту оказания услуги (количество задавайте вручную в строках заказа на продажу). Создайте проект для этого заказа с задачей для каждой строки заказа на продажу, чтобы отслеживать потраченное время."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice this service when it is delivered (set the quantity by hand on your sales order lines). Create a task in an existing project to track the time spent."
msgstr "Выставляйте счет за услугу при ее оказании (задайте количество вручную в строках заказа на продажу). Создайте задачу в существующем проекте, чтобы отслеживать потраченное время."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice this service when it is delivered (set the quantity by hand on your sales order lines). Create an empty project for the order to track the time spent."
msgstr "Выставляйте счет-фактуру по факту оказания услуги (количество задайте вручную в строках заказа на продажу). Создайте пустой проект для этого заказа, чтобы отслеживать потраченное время."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice your milestones when they are reached."
msgstr "Выставляйте счета по достижении основных этапов."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice your milestones when they are reached. Create a project for the order with a task for each sales order line to track the time spent."
msgstr "Выставляйте счета по достижении основных этапов. Создайте проект для заказа с задачей для каждой строки заказа на продажу, чтобы отслеживать потраченное время."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice your milestones when they are reached. Create a task in an existing project to track the time spent."
msgstr "Выставляйте счета по достижении основных этапов. Создайте задачу в существующем проекте, чтобы отслеживать потраченное время."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Invoice your milestones when they are reached. Create an empty project for the order to track the time spent."
msgstr "Выставляйте счета по достижении основных этапов. Создайте пустой проект для заказа, чтобы отслеживать потраченное время."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Invoice your time and material to customers"
msgstr "Выставляйте счета клиентам за потраченное время и материалы"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Invoiced"
msgstr "Счёт-фактура выставлена"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "Invoices"
msgstr "Счета"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Invoicing Policy"
msgstr "Политика выставления счетов"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__is_product_milestone
msgid "Is Product Milestone"
msgstr "Является вехой развития продукта"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move_line
msgid "Journal Item"
msgstr "Наименование в журнале"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Load more"
msgstr "Загрузить больше"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_type
#: model:ir.model.fields,help:sale_project.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Ручная установка количества в заказе: счет-фактура на основе введенного вручную количества, без создания аналитической учетной записи.\n"
"Расписания по контракту: счет-фактура на основе отслеживаемых часов в соответствующем расписании.\n"
"Создание задач и отслеживание часов: создание задачи по проверке заказа клиента и отслеживание рабочего времени."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "Materials"
msgstr "Материалы"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Метод обновления количества поставленных товаров"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__milestone_count
msgid "Milestone Count"
msgstr "Подсчет вех"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_project.selection__sale_order_line__qty_delivered_method__milestones
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Milestones"
msgstr "Этапы"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "New Sales Order Item"
msgstr "Новый элемент заказа на продажу"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Non-billable"
msgstr "Не оплачиваемые"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "Not Billed"
msgstr "Не выставлен счет"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "Ничего"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "Количество проектов"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_task_only_product_template
msgid "Office Furniture Set (task)"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""
"При подтверждении заказа на продажу этот продукт может генерировать проект и/или задачу.         На их основе вы можете отслеживать продаваемую услугу.\n"
"         'В проекте заказа на продажу': Будет использовать настроенный проект заказа на продажу, если он определен, или вернется к созданию нового проекта на основе выбранного шаблона."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid "Operation not supported"
msgstr "Операция не поддерживается"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "Other Services"
msgstr "Другие услуги"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_painting_product_template
msgid "Painting"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__quantity_percentage
msgid "Percentage of the ordered quantity that will automatically be delivered once the milestone is reached."
msgstr "Процент от заказанного количества, которое будет автоматически доставлено по достижении контрольной точки."

#. module: sale_project
#: model:product.template,name:sale_project.product_product_plumbing_product_template
msgid "Plumbing Services"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "Prepaid/Fixed Price"
msgstr "Предоплата/фиксированная цена"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product"
msgstr "Товар"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "Проект"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "Проект и задача"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_milestone
msgid "Project Milestone"
msgstr "Этап проекта"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_type__milestones
msgid "Project Milestones"
msgstr "Вехи проекта"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "Шаблон проекта"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "Порядок реализации проекта"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Projects"
msgstr "Проекты"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "Проекты, используемые в этом заказе на продажу."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__product_uom_qty
msgid "Quantity"
msgstr "Количество"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__quantity_percentage
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "Quantity (%)"
msgstr "Количество (%)"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Строка шаблона котировки"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__reached_milestones_ids
msgid "Reached Milestones"
msgstr "Достигнутые рубежи"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_display_name
msgid "Sale Line Display Name"
msgstr "Отображаемое имя линии продажи"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_count
msgid "Sale Order Count"
msgstr "Счетчик заказов на продажу"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_line_count
msgid "Sale Order Line Count"
msgstr "Счетчик строк заказов на продажу"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Sales"
msgstr "Продажи"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Sales & Invoicing"
msgstr "Продажи и выставление счетов"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project_milestone.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model:project.project,name:sale_project.so_template_project
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Sales Order Item"
msgstr "Пункт заказа клиента"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__sale_line_id
msgid "Sales Order Item that will be updated once the milestone is reached."
msgstr "Элемент заказа на продажу, который будет обновлен после достижения контрольной точки."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added in order to be invoiced to your customer.\n"
"By default the sales order item set on the project will be selected. In the absence of one, the last prepaid sales order item that has time remaining will be used.\n"
"Remove the sales order item in order to make this task non billable. You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"Пункт заказа на продажу, в который будет добавлено время, потраченное на выполнение этой задачи, чтобы выставить счет заказчику.\n"
"По умолчанию будет выбран пункт заказа на продажу, установленный в проекте. При отсутствии такового будет использован последний предоплаченный элемент заказа на продажу, у которого осталось время.\n"
"Чтобы сделать эту задачу неоплачиваемой, удалите элемент заказа на продажу. Вы также можете изменить или удалить пункт заказа на продажу для каждой записи табеля учета рабочего времени в отдельности."

#. module: sale_project
#. odoo-javascript
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Sales Order Items"
msgstr "Пункты заказа на продажу"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "Sales Orders"
msgstr "Заказы на продажу"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"Пункт заказа на продажу, который будет выбран по умолчанию в задачах и табелях учета рабочего времени данного проекта, за исключением случаев, когда сотрудник, указанный в табеле учета рабочего времени, явно связан с другим пунктом заказа на продажу в данном проекте.\n"
"При необходимости его можно изменить в каждой задаче и табеле учета рабочего времени отдельно."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Заказ на продажу, с которым связан проект."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Заказ на продажу, к которому привязана задача."

#. module: sale_project
#: model:product.template,name:sale_project.product_product_screw_driver_product_template
msgid "Screw Driver"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
msgid "Search in Invoice"
msgstr "Поиск в счете-фактуре"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "Поиск в заказе на продажу"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr "Выберите не оплачиваемый проект, по которому можно создавать задания."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
msgid "Select who to bill..."
msgstr "Выберите, кому выставлять счета..."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "Политика выставления счетов за услуги"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__show_create_project_button
msgid "Show Create Project Button"
msgstr "Показать кнопку создания проекта"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__show_project_button
msgid "Show Project Button"
msgstr "Кнопка \"Показать проект"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__show_task_button
msgid "Show Task Button"
msgstr "Показать кнопку задачи"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Sold"
msgstr "Продано"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_state
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_state
msgid "Status"
msgstr "Статус"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "Задача"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Task Created (%s): %s"
msgstr "Задание создано (%s): %s"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Повторение заданий"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model:project.project,label_tasks:sale_project.so_template_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "Задачи"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Анализ задач"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated with this sale"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "The product %s should not have a global project since it will generate a project."
msgstr "Продукт %s не должен иметь глобального проекта, так как он будет генерировать проект."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "The product %s should not have a project nor a project template since it will not generate project."
msgstr "Продукт %s не должен иметь ни проекта, ни шаблона проекта, так как он не будет генерировать проект."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
msgid "The product %s should not have a project template since it will generate a task in a global project."
msgstr "Продукт %s не должен иметь шаблона проекта, так как он будет генерировать задачу в глобальном проекте."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid "The project couldn't be created as the Sales Order must be confirmed, is already linked to a project, or doesn't involve any services."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid "This Sales Order must contain at least one product of type \"Service\"."
msgstr "Этот заказ на продажу должен содержать хотя бы один продукт типа \"Услуга\"."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "This task has been created from: %s (%s)"
msgstr "Это задание было создано из: %s (%s)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "To Do"
msgstr "Сделать"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "Выставить счет"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_type
msgid "Track Service"
msgstr "Служба пути"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Track what you sold, delivered, and invoiced."
msgstr "Отслеживайте, что вы продали, доставили и выставили счет."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__product_uom
msgid "Unit of Measure"
msgstr "Ед. изм"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__vendor_bill_count
msgid "Vendor Bill Count"
msgstr "Счет поставщика"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "Vendor Bills"
msgstr "Счета поставщиков"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "View Sales Order"
msgstr "Просмотр заказа на продажу"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_project_and_task_product_template
msgid "Website Redesign Service (project & task)"
msgstr ""

#. module: sale_project
#: model:product.template,name:sale_project.product_product_wiring_product_template
msgid "Wiring Services"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
msgid "You cannot link the order item %(order_id)s - %(product_id)s to this task because it is a re-invoiced expense."
msgstr "Вы не можете связать элемент заказа %(order_id)s - %(product_id)s с этой задачей, поскольку это повторно выставленный счет."
