# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_adyen
#
# Translators:
# <PERSON>, 2019
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON>, 2019\n"
"Language-Team: Norwegian <PERSON>l (https://www.transifex.com/odoo/teams/41243/"
"nb/)\n"
"Language: nb\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_provider_form
msgid ""
"<strong>Warning:</strong> To capture the amount manually, you also need to "
"set\n"
"                    the Capture Delay to manual on your Adyen account "
"settings."
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "A request was sent to void the transaction with reference %s (%s)."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_api_key
msgid "API Key"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_api_url_prefix
msgid "API URL Prefix"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields.selection,name:payment_adyen.selection__payment_provider__code__adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid ""
"An error occurred during the processing of your payment. Please try again."
msgstr ""

#. module: payment_adyen
#. odoo-javascript
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
msgid "Cannot display the payment form"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_client_key
msgid "Client Key"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__code
msgid "Code"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_hmac_key
msgid "HMAC Key"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_capture_wizard__has_adyen_tx
msgid "Has Adyen Tx"
msgstr ""

#. module: payment_adyen
#. odoo-javascript
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
msgid "Incorrect payment details"
msgstr ""

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_provider_form
msgid "Learn More"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_merchant_account
msgid "Merchant Account"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_capture_wizard
msgid "Payment Capture Wizard"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransaksjon"

#. module: payment_adyen
#. odoo-javascript
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "Received data for child transaction with missing transaction values"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "Received data with invalid payment state: %s"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "Received data with missing merchant reference"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "Received data with missing payment state."
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/controllers/main.py:0
msgid "Received tampered payment request data."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "Shopper Reference"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_api_key
msgid "The API key of the webservice user"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_hmac_key
msgid "The HMAC key of the webhook"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid ""
"The amount processed by Adyen for the transaction %s is different than the "
"one requested. Another transaction is created with the correct amount."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_api_url_prefix
msgid "The base URL for the API endpoints"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "The capture of the transaction with reference %s failed."
msgstr ""

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_capture_wizard_view_form
msgid ""
"The capture or void of the transaction might take a few minutes to be\n"
"                    processed by Adyen and reflected in Odoo."
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid ""
"The capture request of %(amount)s for the transaction with reference %(ref)s "
"has been requested (%(provider_name)s)."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_client_key
msgid "The client key of the webservice user"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_merchant_account
msgid "The code of the merchant account to use with this provider"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_provider.py:0
msgid "The communication with the API failed. Details: %s"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "The unique reference of the partner owning this token"
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "The void of the transaction with reference %s failed."
msgstr ""

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
msgid "Your payment was refused. Please try again."
msgstr ""
