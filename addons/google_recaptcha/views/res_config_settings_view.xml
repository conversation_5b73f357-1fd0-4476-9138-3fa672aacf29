<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.web.recaptcha</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//setting[@id='recaptcha']" position="attributes">
                <attribute name="help" add="If no keys are provided, no checks will be done." separator=" " />
            </xpath>
            <div id="recaptcha_warning" position="replace">
                <div class="content-group" id="reacaptcha_configuration_settings" invisible="not module_google_recaptcha">
                    <div class="mt16 row">
                        <label for="recaptcha_public_key" class="col-3 o_light_label"/>
                        <field name="recaptcha_public_key"/>
                    </div>
                    <div class="mt16 row">
                        <label for="recaptcha_private_key" class="col-3 o_light_label"/>
                        <field name="recaptcha_private_key"/>
                    </div>
                    <div class="mt16 row">
                        <label for="recaptcha_min_score" class="col-3 o_light_label"/>
                        <field name="recaptcha_min_score"/>
                    </div>
                    <div>
                        <a href="https://www.google.com/recaptcha/admin/create" class="oe_link" target="_blank">
                            <i class="oi oi-arrow-right"/> Generate reCAPTCHA v3 keys
                        </a>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>
