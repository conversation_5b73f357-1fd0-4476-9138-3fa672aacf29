# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_holidays
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr "البند التحليلي"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave__timesheet_ids
msgid "Analytic Lines"
msgstr "البنود التحليلية"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
msgid ""
"Both the internal project and task are required to generate a timesheet for "
"the time off %s. If you don't want a timesheet, you should leave the "
"internal project and task empty."
msgstr ""
"المشروع الداخلي والمهمة كلاهما مطلوبان لإنشاء جدول زمني للإجازة %s. إذا كنت "
"لا تريد جدولاً زمنياً، عليك ترك المشروع الداخلي والمهمة فارغين. "

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_employee
msgid "Employee"
msgstr "الموظف"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid "Generate Timesheets"
msgstr "إنشاء الجداول الزمنية "

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Generate timesheets when validating time off requests of this type"
msgstr "إنشاء جداول زمنية عند تصديق طلبات الإجازات من هذا النوع "

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid ""
"If checked, when validating a time off, timesheet will be generated in the "
"Vacation Project of the company."
msgstr ""
"إذا كان محدداً، سوف يتم إنشاء جدول زمني في مشروع الإجازة في الشركة عند تصديق"
" أيام الإجازة. "

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/__init__.py:0
msgid "Internal"
msgstr "داخلي"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid "Internal Project"
msgstr "مشروع داخلي"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/project_task.py:0
msgid "Operation not supported"
msgstr "العملية غير مدعومة "

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "المشروع"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "تفاصيل إجازة المَورد "

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_project_task
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_task_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "المهمة"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid ""
"The default project used when automatically generating timesheets via time "
"off requests. You can specify another project on each time off type "
"individually."
msgstr ""
"المشروع الافتراضي الذي يتم استخدامه عند إنشاء الجداول الزمنية تلقائياً عن "
"طريق طلبات الإجازة. يمكنك تحديد مشروع آخر في كل نوع إجازة على حدة. "

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid ""
"The default task used when automatically generating timesheets via time off "
"requests. You can specify another task on each time off type individually."
msgstr ""
"المهمة الافتراضية التي يتم استخدامها عند إنشاء الجداول الزمنية تلقائياً عن "
"طريق طلبات الإجازة. يمكنك تحديد مهمة أخرى في كل نوع إجازة على حدة. "

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/__init__.py:0
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave
msgid "Time Off"
msgstr "الإجازات "

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#: code:addons/project_timesheet_holidays/models/resource_calendar_leaves.py:0
msgid "Time Off (%(index)s/%(total)s)"
msgstr "الإجازة (%(index)s/%(total)s) "

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid "Time Off Task"
msgstr "مهمة الإجازة "

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave_type
msgid "Time Off Type"
msgstr "نوع الإجازة "

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_project_task__leave_types_count
msgid "Time Off Types Count"
msgstr "عدد أنواع الإجازات "

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Timesheets"
msgstr "الجداول الزمنية "

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid "View Time Off"
msgstr "عرض الإجازات "

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid ""
"You cannot create timesheets for a task that is linked to a time off type. "
"Please use the Time Off application to request new time off instead."
msgstr ""
"لا يمكنك إنشاء جداول زمنية لمهمة مرتبطة بنوع إجازة. يرجى استخدام تطبيق "
"الإجازات لطلب إجازة جديدة عوضاً عن ذلك. "

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid ""
"You cannot delete timesheets that are linked to time off requests. Please "
"cancel your time off request from the Time Off application instead."
msgstr ""
"لا يمكنك حذف جداول زمنية مرتبطة بطلبات إجازات. يرجى إلغاء طلب الإجازة من "
"تطبيق الإجازات عوضاً عن ذلك. "

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid ""
"You cannot modify timesheets that are linked to time off requests. Please "
"use the Time Off application to modify your time off requests instead."
msgstr ""
"لا يمكنك تعديل الجداول الزمنية المرتبطة بطلب إجازة. يرجى استخدام تطبيق "
"الإجازات لتعديل طلبات إجازاتك عوضاً عن ذلك. "
