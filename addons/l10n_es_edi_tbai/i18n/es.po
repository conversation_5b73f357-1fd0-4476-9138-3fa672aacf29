# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_edi_tbai
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-09 09:10+0000\n"
"PO-Revision-Date: 2023-08-09 09:10+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner_recibidas
msgid "1.0"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner
msgid "1.1"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner_recibidas
msgid "240"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move_reversal
msgid "Account Move Reversal"
msgstr "Reversión de asiento"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_ir_attachment
msgid "Attachment"
msgstr "Adjunto"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_refund_reason
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_refund_reason
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move_reversal__l10n_es_tbai_refund_reason
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_refund_reason
msgid ""
"BOE-A-1992-28740. Ley 37/1992, de 28 de diciembre, del Impuesto sobre el "
"Valor Añadido. Artículo 80. Modificación de la base imponible."
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_cancel_xml
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_cancel_xml
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_cancel_xml
msgid "Cancellation XML"
msgstr "XML de cancelación"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_cancel_xml
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_cancel_xml
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_cancel_xml
msgid ""
"Cancellation XML sent to TicketBAI. Kept if accepted or no response "
"(timeout), cleared otherwise."
msgstr ""
"El XML de cancelación se envió a TicketBAI. Se mantiene si se acepta o no se"
" obtiene respuesta (timeout), de lo contrario, se borra."

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_edi_format
msgid "EDI format"
msgstr "Formato EDI"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr "Documento electrónico de un account.move"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__res_company__l10n_es_tbai_tax_agency__araba
msgid "Hacienda Foral de Araba"
msgstr "Hacienda Foral de la Diputación de Álava"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__res_company__l10n_es_tbai_tax_agency__bizkaia
msgid "Hacienda Foral de Bizkaia"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__res_company__l10n_es_tbai_tax_agency__gipuzkoa
msgid "Hacienda Foral de Gipuzkoa"
msgstr "Diputación Foral de Gipuzkoa"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_reversed_ids
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_reversed_ids
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_reversed_ids
msgid ""
"In the case where a vendor refund has multiple original invoices, you can "
"set them here. "
msgstr "En el caso de que una factura rectificativa de proveedor tenga varias facturas "
"reembolsadas, puede establecerlas aquí."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
#, python-format
msgid ""
"In case of a foreign customer, you need to configure the tax scope on taxes:\n"
"%s"
msgstr "En el caso de un cliente extranjero, es necesario configurar el ámbito fiscal en impuestos:\n"
"%s"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_refund_reason
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_refund_reason
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__l10n_es_tbai_refund_reason
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_refund_reason
msgid "Invoice Refund Reason Code (TicketBai)"
msgstr "Código de motivo de reembolso de la factura (TicketBai)"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_chain_index
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_chain_index
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_chain_index
msgid ""
"Invoice index in chain, set if and only if an in-chain XML was submitted and"
" did not error"
msgstr ""
"Índice de facturas en cadena que se establece si y solo si se envió un XML y"
" no dio error."

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__l10n_es_tbai_is_required
msgid "Is TicketBai required for this reversal"
msgstr "¿Se necesita TicketBai para realizar una reversión?"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_is_required
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_is_required
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_is_required
msgid "Is the Basque EDI (TicketBAI) needed ?"
msgstr "¿Se necesita el EDI vasco (TicketBai)?"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Licence NIF"
msgstr "Licencia de NIF"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Licence number"
msgstr "Número de licencia"

#. module: l10n_es_edi_tbai
#: model:ir.ui.menu,name:l10n_es_edi_tbai.menu_l10n_es_edi_tbai_license
msgid "Licenses (TicketBAI)"
msgstr "Licencias (TicketBai)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
msgid "No XML response received from LROE."
msgstr "No se recibió respuesta XML de LROE."

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "No tax agency selected: TicketBAI not activated."
msgstr "No se seleccionó ninguna agencia tributaria: no se activó TicketBai."

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_l10n_es_edi_certificate
msgid "Personal Digital Certificate"
msgstr "Certificado personal de persona física"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
msgid "Please configure the Tax ID on your company for TicketBAI."
msgstr "Configure el NIF en su empresa par TicketBai."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
msgid "Please configure the certificate for TicketBAI/SII."
msgstr "Configure el certificado para TicketBai/SII."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
msgid "Please specify a tax agency on your company for TicketBAI."
msgstr "Especifique una agencia tributaria para su empresa en TicketBai."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Production license"
msgstr "Licencia de producción"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r1
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r1
msgid "R1: Art. 80.1, 80.2, 80.6 and rights founded error"
msgstr "R1: Art. 80.1, 80.2, 80.6 y por error fundado de derecho"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r2
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r2
msgid "R2: Art. 80.3"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r3
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r3
msgid "R3: Art. 80.4"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r4
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r4
msgid "R4: Art. 80 - other"
msgstr "R4: Art. 80 - resto"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r5
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r5
msgid "R5: Factura rectificativa en facturas simplificadas"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_invoice_desglose
msgid "RL"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
msgid "Refund reason cannot be R5 for non-simplified invoices (TicketBAI)"
msgstr ""
"El motivo de reembolso no puede ser R5 para facturas no simplificadas "
"(TicketBai)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
msgid "Refund reason must be R5 for simplified invoices (TicketBAI)"
msgstr ""
"El motivo de reembolso debe ser R5 para facturas simplificadas (TicketBai)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
msgid "Refund reason must be specified (TicketBAI)"
msgstr "Se debe especificar el motivo del reembolso (TicketBai)"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_reversed_ids
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_reversed_ids
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_reversed_ids
msgid "Refunded Vendor Bills"
msgstr "Facturas Proveedor Reembolsadas"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Registro de Libros connection SII/TicketBAI"
msgstr "Conexión Registro de Libros SII/TicketBAI"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Software name"
msgstr "Nombre del software"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Software version"
msgstr "Versión del software"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_post_xml
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_post_xml
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_post_xml
msgid "Submission XML"
msgstr "XML de envío"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_post_xml
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_post_xml
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_post_xml
msgid ""
"Submission XML sent to TicketBAI. Kept if accepted or no response (timeout),"
" cleared otherwise."
msgstr ""
"Se envió el XML de envío a TicketBai. Se mantiene si se acepta o no se "
"obtiene respuesta (timeout), de lo contrario, se borra."

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_invoice_bundle
msgid "TEST-DEVICE-001"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_tax_agency
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__l10n_es_tbai_tax_agency
msgid "Tax Agency for TBAI"
msgstr "Agencia tributaria TBAI"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Tax agency selected: TicketBAI is activated."
msgstr "Agencia tributaria seleccionada: TicketBai está activado."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Test license (Araba)"
msgstr "Licencia de prueba (Araba)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Test license (Bizkaia)"
msgstr "Licencia de prueba (Bizkaia)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Test license (Gipuzkoa)"
msgstr "Licencia de prueba (Gipuzkoa)"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.view_move_form_inherit_l10n_es_edi_tbai
msgid "TicketBAI"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_chain_index
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_chain_index
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_chain_index
msgid "TicketBAI chain index"
msgstr "Índice de TicketBai en cadena"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "TicketBAI is not configured"
msgstr "TicketBai no está configurado"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_license_html
msgid "TicketBAI license"
msgstr "Licencia de TicketBai"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_is_required
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_is_required
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_payment__l10n_es_tbai_is_required
msgid "TicketBAI required"
msgstr "Se requiere TicketBai"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
#, python-format
msgid ""
"TicketBAI: Cannot post a reversal move while the source document (%s) has "
"not been posted"
msgstr ""
"TicketBAI: No se puede contabilizar un movimiento de anulación mientras no "
"se haya contabilizado el documento de origen (%s)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_edi_format.py:0
#, python-format
msgid ""
"TicketBAI: Cannot post invoice while chain head (%s) has not been posted"
msgstr ""
"TicketBAI: No se puede contabilizar la factura mientras no se haya "
"contabilizado la cabeza de cadena (%s)"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_chain_sequence_id
msgid "TicketBai account.move chain sequence"
msgstr "Secuencia en cadena TicketBai account.move"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid "You cannot delete a move that has a TicketBAI chain id."
msgstr ""
"No puede eliminar un movimiento que tiene un ID en cadena de TicketBai."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid ""
"You cannot reset to draft an entry that has been posted to TicketBAI's chain"
msgstr ""
"No puede regresar a borrador un asiento que se publicó en la cadena de "
"TicketBai."
