# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_aps
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_access_code
msgid "APS Access Code"
msgstr "Код доступу APS"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_merchant_identifier
msgid "APS Merchant Identifier"
msgstr "Ідентифікатор мерчанту APS"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_sha_request
msgid "APS SHA Request Phrase"
msgstr "Фраза запиту APS SHA"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_sha_response
msgid "APS SHA Response Phrase"
msgstr "Фраза відповіді APS SHA"

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "Access Code"
msgstr "Код доступу"

#. module: payment_aps
#: model:ir.model.fields.selection,name:payment_aps.selection__payment_provider__code__aps
msgid "Amazon Payment Services"
msgstr "Платіжні послуги Amazon"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__code
msgid "Code"
msgstr "Код"

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "Merchant Identifier"
msgstr "Ідентифікатор мерчанту"

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Не знайдено жодної транзакції, що відповідає референсу %s."

#. module: payment_aps
#: model:ir.model,name:payment_aps.model_payment_provider
msgid "Payment Provider"
msgstr "Провайдер платежу"

#. module: payment_aps
#: model:ir.model,name:payment_aps.model_payment_transaction
msgid "Payment Transaction"
msgstr "Платіжна операція"

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
msgid "Received data with missing payment state."
msgstr "Отримані дані з відсутнім статусом платежу."

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
msgid "Received data with missing reference %(ref)s."
msgstr "Отримані дані з відсутнім референсом %(ref)s."

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
msgid ""
"Received invalid transaction status %(status)s and reason '%(reason)s'."
msgstr ""
"Отримано недійсний статус транзакції %(status)s та причина '%(reason)s'."

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "SHA Request Phrase"
msgstr "Фраза запиту SHA"

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "SHA Response Phrase"
msgstr "Фраза запиту SHA"

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__aps_access_code
msgid "The access code associated with the merchant account."
msgstr "Код доступу, пов’язаний з обліковим записом мерчанту."

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__aps_merchant_identifier
msgid "The code of the merchant account to use with this provider."
msgstr "Код облікового запису мерчанта для використання з цим провайдером."

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Технічний код цього провайдера платежу."
