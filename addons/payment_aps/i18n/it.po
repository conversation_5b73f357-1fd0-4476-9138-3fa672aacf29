# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_aps
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_access_code
msgid "APS Access Code"
msgstr "Codice di accesso APS"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_merchant_identifier
msgid "APS Merchant Identifier"
msgstr "Identificativo commerciante APS"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_sha_request
msgid "APS SHA Request Phrase"
msgstr "APS frase di richiesta SHA"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_sha_response
msgid "APS SHA Response Phrase"
msgstr "APS frase di risposta SHA"

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "Access Code"
msgstr "Codice di accesso"

#. module: payment_aps
#: model:ir.model.fields.selection,name:payment_aps.selection__payment_provider__code__aps
msgid "Amazon Payment Services"
msgstr "Servizi di pagamento Amazon"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__code
msgid "Code"
msgstr "Codice"

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "Merchant Identifier"
msgstr "Identificativo commerciante"

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Nessuna transazione trovata corrispondente al riferimento %s."

#. module: payment_aps
#: model:ir.model,name:payment_aps.model_payment_provider
msgid "Payment Provider"
msgstr "Fornitore di pagamenti"

#. module: payment_aps
#: model:ir.model,name:payment_aps.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transazione di pagamento"

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
msgid "Received data with missing payment state."
msgstr "Dati ricevuti con stato di pagamento mancante."

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
msgid "Received data with missing reference %(ref)s."
msgstr "Dati ricevuti privi di riferimento %(ref)s."

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
msgid ""
"Received invalid transaction status %(status)s and reason '%(reason)s'."
msgstr ""
"Stato transazione %(status)s e ragione '%(reason)s' ricevuti non validi."

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "SHA Request Phrase"
msgstr "Frase di richiesta SHA"

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "SHA Response Phrase"
msgstr "Frase di risposta SHA"

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__aps_access_code
msgid "The access code associated with the merchant account."
msgstr "Il codice di accesso associate all'account del commerciante."

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__aps_merchant_identifier
msgid "The code of the merchant account to use with this provider."
msgstr ""
"Il codice del conto del commerciante da utilizzare con questo fornitore."

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Codice tecnico del fornitore di pagamenti."
