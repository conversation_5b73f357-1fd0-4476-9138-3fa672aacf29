<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-inherit="web_editor.LinkDialog" t-inherit-mode="extension">
        <xpath expr="//*[@id='o_link_dialog_url_input']" position="after">
            <small class="form-text text-muted">Hint: Type '/' to search an existing page and '#' to link to an anchor.</small>
        </xpath>
    </t>

    <t t-inherit="web_editor.LinkTools" t-inherit-mode="extension">
        <xpath expr="//we-row[@id='url_row']" position="after">
            <div style="text-align: center" t-attf-class="#{state.isButton ? ' d-none' : ''}">
                <small>
                    Type '<span class="highlighted-text">/</span>' to search a page.
                    '<span class="highlighted-text">#</span>' to link to an anchor.
                </small>
            </div>
            <we-row class="o_link_dialog_page_anchor d-none" t-attf-class="#{state.isButton || state.isDocument ? ' d-none' : ''}">
                <we-select class="o_we_user_value_widget o_we_sublevel_1">
                    <we-title>Page Anchor</we-title>
                    <div>
                        <div class="dropdown">
                            <button class="dropdown-toggle"
                                data-bs-toggle="dropdown" tabindex="-1" aria-expanded="false">
                                <we-toggler>
                                    Loading...
                                </we-toggler>
                            </button>
                            <we-selection-items name="link_anchor" class="dropdown-menu link-style">
                                <we-button class="dropdown-item o_anchors_loading">Loading...</we-button>
                            </we-selection-items>
                            <span class="o_we_dropdown_caret"></span>
                        </div>
                    </div>
                </we-select>
            </we-row>
        </xpath>
    </t>

    <t t-inherit="web_editor.toolbar" t-inherit-mode="extension">
        <xpath expr="//div[@id='open-chatgpt']" position="inside">
            <span>AI</span>
        </xpath>

        <xpath expr="//div[@id='strikethrough']" position="attributes">
            <attribute name="t-if">!props.showTextHighlights</attribute>
        </xpath>
    </t>
</templates>
