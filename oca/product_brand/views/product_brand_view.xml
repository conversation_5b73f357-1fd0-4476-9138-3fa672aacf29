<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="product_brand_search_form_view" model="ir.ui.view">
        <field name="name">product.brand.search.form</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <search string="Product Brand">
                <field name="name" />
                <field name="partner_id" />
            </search>
        </field>
    </record>
    <record id="action_open_brand_products" model="ir.actions.act_window">
        <field name="name">Brand Products</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">kanban,form,list</field>
        <field name="domain">[('product_brand_id', '=', active_id)]</field>
    </record>
    <record id="action_open_single_product_brand" model="ir.actions.act_window">
        <field name="name">Product Brand</field>
        <field name="res_model">product.brand</field>
        <field name="view_mode">kanban,form,list</field>
        <field name="target">current</field>
        <field name="domain">[('product_ids', 'in', active_id)]</field>
    </record>
    <record id="view_product_brand_form" model="ir.ui.view">
        <field name="name">product.brand.form</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button
                            name="%(action_open_brand_products)d"
                            type="action"
                            class="oe_stat_button"
                            icon="fa-cubes"
                            context="{'default_product_brand_id': id}"
                        >
                            <field
                                name="products_count"
                                widget="statinfo"
                                string="Products"
                            />
                        </button>
                    </div>
                    <field name="logo" widget="image" class="oe_avatar" />
                    <div class="oe_title">
                        <label for="name" string="Brand Name" class="oe_edit_only" />
                        <h1>
                            <field name="name" />
                        </h1>
                    </div>
                    <group name="main">
                        <group name="partner">
                            <field name="partner_id" />
                        </group>
                    </group>
                    <notebook>
                        <page name="description" string="Description">
                            <field name="description" nolabel="1" />
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_product_brand_tree" model="ir.ui.view">
        <field name="name">product.brand.tree</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <list>
                <field name="name" />
                <field name="description" />
                <field name="partner_id" />
            </list>
        </field>
    </record>
    <record id="view_product_brand_kanban" model="ir.ui.view">
        <field name="name">product.brand.kanban</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="logo" />
                <field name="products_count" />
                <templates>
                    <t t-name="card" class="flex-row">
                        <aside class="w-25 p-1">
                            <field name="logo" widget="image" alt="Avatar" />
                        </aside>
                        <main class="w-100 ps-2 pt-1">
                            <field name="name" class="fw-bolder" />
                            <a
                                name="%(product_brand.action_open_brand_products)d"
                                type="action"
                            >
                                <t t-esc="record.products_count.value" />
                                Products
                            </a>
                        </main>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="view_product_template_search_brand" model="ir.ui.view">
        <field name="name">product.template.search.brand</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view" />
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="product_brand_id" />
                <filter
                    string="Brand"
                    name="groupby_brand"
                    domain="[]"
                    context="{'group_by' : 'product_brand_id'}"
                />
                <separator />
            </field>
        </field>
    </record>
    <record id="product_template_form_brand_add" model="ir.ui.view">
        <field name="name">product.template.product.form</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view" />
        <field name="arch" type="xml">
            <field name="sale_ok" position="before">
                <field name="product_brand_id" placeholder="Brand" />
                <span />
            </field>
        </field>
    </record>
    <record id="view_product_template_kanban_brand" model="ir.ui.view">
        <field name="name">product kanban view: add brand</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_kanban_view" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']/.." position="after">
                <div>
                    <a
                        t-if="record.product_brand_id"
                        type="action"
                        name="%(action_open_single_product_brand)d"
                    >
                        <field name="product_brand_id" />
                    </a>
                </div>
            </xpath>
        </field>
    </record>
    <record id="view_product_variant_kanban_brand" model="ir.ui.view">
        <field name="name">product variant kanban view: add brand</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_kanban_view" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']/.." position="after">
                <div>
                    <a t-if="record.product_brand_id" type="open">
                        <field name="product_brand_id" widget="many2one" />
                    </a>
                </div>
            </xpath>
        </field>
    </record>
    <record id="view_product_template_tree_brand" model="ir.ui.view">
        <field name="name">product tree view: add brand</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view" />
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="product_brand_id" />
            </field>
        </field>
    </record>
    <record id="view_product_variant_tree_brand" model="ir.ui.view">
        <field name="name">product variant tree view: add brand</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_product_tree_view" />
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="product_brand_id" />
            </field>
        </field>
    </record>
    <record model="ir.actions.act_window" id="action_product_brand">
        <field name="name">Brand</field>
        <field name="res_model">product.brand</field>
        <field name="view_mode">kanban,form,list</field>
    </record>
    <menuitem
        name="Product Brands"
        id="menu_product_brand"
        action="action_product_brand"
        parent="sale.prod_config_main"
    />
</odoo>
