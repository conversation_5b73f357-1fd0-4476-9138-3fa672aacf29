from odoo import models, fields, _

class IotSoundSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    sound_device_id = fields.Many2one(
        'iot.device',
        string='Default Sound Device',
        config_parameter='iot_sound.default_sound_device_id',
        help='Select the default sound device for IoT sound settings'
    )

    server_api_url = fields.Char(
        string='Server API URL',
        config_parameter='iot.server_api_url',
        help='Enter the server API URL for IoT sound settings'
    )

    def create_attachment(self, file_name, base64_data):
        """Create an attachment in the database."""
        existing_attachment = self.env['ir.attachment'].search([('name', '=', file_name)], limit=1)

        if not existing_attachment:
            attachment = self.env['ir.attachment'].create([{
                'name' : file_name,
                'datas' : base64_data,
                'is_sound' : True}]
            )
            return attachment




    def action_download_sound_files(self):
        # device_id_config= iot_sound.default_sound_device_id
        #device=self.env['iot.device'].browse()
        response = self.env["iot.sound_client_api"].get_files(self.sound_device_id.device_service_url)

        created_count = 0

        if response.get('response_code') == "1":
            sound_list = response.get('response_result',[])

            for sound in sound_list:
                file_name = sound["file_name"]
                base64_data = sound["file_data"]

                # Create attachment
                if base64_data:
                    created = self.create_attachment(file_name, base64_data)
                    if created:
                        created_count += 1

            if created_count > 0:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Notification'),
                        'message': _("%d attachment(s) created successfully.") % created_count,
                        'type': 'info',
                        'sticky': False,
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Notification'),
                        'message': _("No new attachments were created."),
                        'type': 'danger',
                        'sticky': False,
                    }
                }
