from .utils import *
from .dto_response import Response , BaseResponse
from dataclasses import dataclass
from typing import Any, Optional, List, TypeVar, Type, cast, Callable

class CardType:
    id: int
    name: str
    type: int

    def __init__(self, id: int, name: str, type: int) -> None:
        self.id = id
        self.name = name
        self.type = type

    @staticmethod
    def from_dict(obj: Any) -> 'CardType':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        type = to_int(from_str(obj.get("type")))
        return CardType(id, name, type)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["type"] = from_str(str(self.type))
        return result

class UserID:
    user_id: int
    name: str

    def __init__(self, user_id: int, name: str) -> None:
        self.user_id = user_id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'UserID':
        if not obj:
            return None
        user_id = to_int(from_str(obj.get("user_id")))
        name = from_str(obj.get("name"))
        return UserID(user_id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["user_id"] = from_str(str(self.user_id))
        result["name"] = from_str(self.name)
        return result

class WiegandFormatID:
    id: int
    name: Optional[str]

    def __init__(self, id: int, name: Optional[str]) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'WiegandFormatID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_union([from_str, from_none], obj.get("name"))
        return WiegandFormatID(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        if self.name is not None:
            result["name"] = from_union([from_str, from_none], self.name)
        return result

class Row:
    id: int
    card_id: str
    display_card_id: str
    status: int
    is_blocked: bool
    is_assigned: bool
    card_type: CardType
    mobile_card: bool
    issue_count: int
    card_slot: int
    card_mask: int
    wiegand_format_id: WiegandFormatID
    user_id: Optional[UserID]

    def __init__(self, id: int, card_id: str, display_card_id: str, status: int, is_blocked: bool, is_assigned: bool, card_type: CardType, mobile_card: bool, issue_count: int, card_slot: int, card_mask: int, wiegand_format_id: WiegandFormatID, user_id: Optional[UserID]) -> None:
        self.id = id
        self.card_id = card_id
        self.display_card_id = display_card_id
        self.status = status
        self.is_blocked = is_blocked
        self.is_assigned = is_assigned
        self.card_type = card_type
        self.mobile_card = mobile_card
        self.issue_count = issue_count
        self.card_slot = card_slot
        self.card_mask = card_mask
        self.wiegand_format_id = wiegand_format_id
        self.user_id = user_id

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        card_id = from_str(obj.get("card_id"))
        display_card_id = from_str(obj.get("display_card_id"))
        status = to_int(from_str(obj.get("status")))
        is_blocked = from_stringified_bool(from_str(obj.get("is_blocked")))
        is_assigned = from_stringified_bool(from_str(obj.get("is_assigned")))
        card_type = CardType.from_dict(obj.get("card_type"))
        mobile_card = from_stringified_bool(from_str(obj.get("mobile_card")))
        issue_count = to_int(from_str(obj.get("issue_count")))
        card_slot = to_int(from_str(obj.get("card_slot")))
        card_mask = to_int(from_str(obj.get("card_mask")))
        wiegand_format_id = WiegandFormatID.from_dict(obj.get("wiegand_format_id"))
        user_id = from_union([UserID.from_dict, from_none], obj.get("user_id"))
        return Row(id, card_id, display_card_id, status, is_blocked, is_assigned, card_type, mobile_card, issue_count,
                   card_slot, card_mask, wiegand_format_id, user_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["card_id"] = from_str(self.card_id)
        result["display_card_id"] = from_str(self.display_card_id)
        result["status"] = from_str(str(self.status))
        result["is_blocked"] = from_str(str(self.is_blocked).lower())
        result["is_assigned"] = from_str(str(self.is_assigned).lower())
        result["card_type"] = to_class(CardType, self.card_type)
        result["mobile_card"] = from_str(str(self.mobile_card).lower())
        result["issue_count"] = from_str(str(self.issue_count))
        result["card_slot"] = from_str(str(self.card_slot))
        result["card_mask"] = from_str(str(self.card_mask))
        result["wiegand_format_id"] = to_class(WiegandFormatID, self.wiegand_format_id)
        if self.user_id is not None:
            result["user_id"] = from_union([lambda x: to_class(UserID, x), from_none], self.user_id)
        return result

class CardCollection:
    rows: List[Row]
    total: int

    def __init__(self, rows: List[Row], total: int) -> None:
        self.rows = rows
        self.total = total

    @staticmethod
    def from_dict(obj: Any) -> 'CardCollection':
        if not obj:
            return None
        rows = from_list(Row.from_dict, obj.get("rows"))
        total = to_int(from_str(obj.get("total")))
        return CardCollection(rows, total)

    def to_dict(self) -> dict:
        result: dict = {}
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        result["total"] = from_str(str(self.total))
        return result

class CardsResponse(BaseResponse):
    card_collection: CardCollection
    response: Response

    def __init__(self, card_collection: CardCollection, response: Response) -> None:
        self.card_collection = card_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'CardsResponse':
        if not obj:
            return None
        card_collection = CardCollection.from_dict(obj.get("CardCollection"))
        response = Response.from_dict(obj.get("Response"))
        return CardsResponse(card_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["CardCollection"] = to_class(CardCollection, self.card_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def cards_response_from_dict(s: Any) -> CardsResponse:
    return CardsResponse.from_dict(s)


def cards_response_to_dict(x: CardsResponse) -> Any:
    return to_class(CardsResponse, x)



