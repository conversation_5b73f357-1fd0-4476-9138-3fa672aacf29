Here’s the organized and formatted version of your document for better readability and clarity:

---

# **Odoo LDAP Configuration - Summary Documentation**

---

## **1. Overview**
This document provides a concise summary of configuring Odoo to authenticate users via OpenLDAP. It also addresses potential timeout issues and server crashes caused by LDAP authentication delays.

---

## **2. Odoo LDAP Configuration**

### **2.1 Server Information**
| Parameter          | Value            |
|--------------------|------------------|
| LDAP Server Address| 127.0.0.1        |
| LDAP Server Port   | 389              |
| Use TLS?           | NO               |

---

### **2.2 Login Information**
| Parameter       | Value                          |
|-----------------|--------------------------------|
| LDAP Bind DN    | cn=admin,dc=laplace,dc=com     |
| LDAP Password   | admin_pass                     |

---

### **2.3 Process Parameters**
| Parameter       | Value                          |
|-----------------|--------------------------------|
| LDAP Base       | dc=laplace,dc=com              |
| LDAP Filter     | (uid=%s)                       |
| Sequence        | 5                              |

---

### **2.4 User Example**
| Parameter             | Value                                                                 |
|-----------------------|-----------------------------------------------------------------------|
| LDAP User DN          | cn=lpdap_user Test,cn=Developers Groups,ou=IT Dept,dc=laplace,dc=com |
| Username (Odoo Login) | lpdap_user                                                           |
| Password              | 123456                                                               |

---

## **3. Troubleshooting: LDAP Timeout & Server Down Issue**

### **3.1 Problem Description**
- Odoo times out when trying to authenticate users via LDAP.
- The server becomes unresponsive due to long LDAP query processing times.
- No logs appear in the OpenLDAP container during authentication attempts.

---

### **3.2 Debugging Steps**

#### **✅ Check LDAP Connection**
1. **Verify Port Availability**
   ```bash
   nc -zv 127.0.0.1 389
   ```
   - Expected Output:
     ```
     Connection to 127.0.0.1 port 389 [tcp/ldap] succeeded!
     ```

2. **Test LDAP Query from Terminal**
   ```bash
   ldapsearch -x -H ldap://127.0.0.1:389 -D "cn=admin,dc=laplace,dc=com" -w admin_pass -b "dc=laplace,dc=com" "(uid=lpdap_user)"
   ```
   - Expected Output:
     - The user entry should be retrieved successfully.
     - If not, verify bind credentials, base DN, and filter syntax.

---

#### **✅ Check Odoo Configuration**
- Change the LDAP filter from:
  ```
  (cn=%s)
  ```
  To:
  ```
  (uid=%s)
  ```
  - **Reason**:
    - `cn` may include spaces (e.g., `lpdap_user Test`), which can cause authentication issues.
    - `uid` provides a unique, simpler identifier (`lpdap_user`).
    - Use the username (`uid`) in Odoo instead of the full email (`<EMAIL>`).

---

#### **✅ Monitor OpenLDAP Logs**
If authentication fails, check OpenLDAP logs:
```bash
docker logs -f openldap-container-name
```

---

#### **✅ Adjust Odoo LDAP Timeout Settings**
Modify the Odoo LDAP timeout setting to prevent server crashes:
1. Increase the timeout in Odoo config (`odoo.conf`):
   ```
   [options]
   ldap_timeout = 30  # Increase timeout (default is 10)
   ```
2. Restart Odoo for changes to apply:
   ```bash
   docker restart odoo-container-name
   ```

---

#### **✅ Optimize OpenLDAP Performance**
If queries are slow, optimize OpenLDAP indexing:
1. Modify `slapd.conf` or `olcDatabase={1}mdb.ldif` to add indexing:
   ```
   index uid eq
   index cn eq
   ```
2. Restart OpenLDAP:
   ```bash
   docker restart openldap-container-name
   ```

---

## **4. Conclusion**
- The timeout issue was resolved by changing the LDAP filter to `(uid=%s)` and ensuring users log in with their `uid` instead of an email.
- To prevent server downtime, increase Odoo’s LDAP timeout (`ldap_timeout = 30`).
- OpenLDAP performance can be improved by adding proper indexing.

If issues persist, check:
- Odoo logs (`odoo.log`)
- LDAP logs (`docker logs openldap-container-name`)  
for further debugging.

---

**🚀 End of Document**