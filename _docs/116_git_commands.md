# Git Commands 
Repo URL: https://dev.azure.com/laplace-dev/_git/addons_mdu 

### Git Project Intilization Steps:
    >> cd /home/<USER>/Documents/Laplace/Projects/odoo17
    >> git clone https://<user>:<EMAIL>/laplace-dev/addons_mdu/_git/addons_mdu
    >> git remote set-url origin https://<user>:<EMAIL>/laplace-dev/addons_mdu/_git/addons_mdu 
    >> cd addons_mdu
    >> git checkout dev17
    >> git branch <yourbranch>
    >> git checkout<yourbranch>
    >> git push -u origin <yourbranch>

### Daily Git Project Common Commands
    - Confirm you are in your branch
        >> git status
        >> git checkout <yourbranch>
    - Start of the day to pull update from team & merge
        >> git pull
        >> git merge origin/dev17
    - End of the day to push updates ( in case no syntax error )
        >> git pull
        >> git merge origin/dev17
        >> git status
        >> git add .
        >> git commit -m "commit title" -m "commit body ref #000"
        >> git push -u origin <yourbranch>


### chat gpt commit description 
Can extract git commit description for coding point only from  this story with noyappy 
