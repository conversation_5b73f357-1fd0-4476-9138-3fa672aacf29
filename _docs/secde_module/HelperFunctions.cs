using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SecDE.Module
{
    public class HelperFunctions
    {
        static public String AdjustString(String str, int len)
        {
            return str.Substring(0, Math.Min(len, str.Length));
        }

        static public string GetHijri(DateTime date)
        {
            if (date == DateTime.MinValue)
                return "";
            CultureInfo cul = new CultureInfo("ar-SA");
            UmAlQuraCalendar h = new UmAlQuraCalendar();
            cul.DateTimeFormat.Calendar = h;
            return date.ToString(@"dd/MM/yyyy", cul);
        }

        static public string GetHijri(DateTime date,string format= @"dd/MM/yyyy")
        {
            if (date == DateTime.MinValue)
                return "";
            CultureInfo cul = new CultureInfo("ar-SA");
            UmAlQuraCalendar h = new UmAlQuraCalendar();
            cul.DateTimeFormat.Calendar = h;
            return date.ToString(format, cul);
        }

        static public DateTime GetGerogrian(string hjDate)
        {
            if (String.IsNullOrEmpty(hjDate) || hjDate.Equals(""))
                return DateTime.MinValue;
            CultureInfo cul = new CultureInfo("ar-SA");
            UmAlQuraCalendar h = new UmAlQuraCalendar();
            cul.DateTimeFormat.Calendar = h;
            DateTime date1 = DateTime.Parse(hjDate, cul);
            return date1;
        }

        static public string GerogrianString(DateTime date, string format = @"dd/MM/yyyy")
        {
            CultureInfo cul = new CultureInfo("en-US");
            GregorianCalendar h = new GregorianCalendar();
            cul.DateTimeFormat.Calendar = h;
            return date.ToString(format, cul);
        }

        static public TimeSpan TrimTime(TimeSpan ts)
        {
            return new TimeSpan(ts.Hours, ts.Minutes, 0);
        }
    }
}
