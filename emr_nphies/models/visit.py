# -*- coding: utf-8 -*-
import datetime

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class Visit(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = 'emr_op.visit'

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_checkout(self):
        """
        Perform the checkout action for the current visit.
        This function filters diagnosis codes to find the principal diagnosis.
        If there is more than one principal diagnosis or none at all, it raises a validation error.
        Then it calls the superclass's action_checkout method and returns the result.
        """
        diagnosis_codes = self.claim_request_manager_id.diagnosis_line_ids.filtered(
            lambda line: line.diagnosis_type_id.code == 'principal')
        msg = "You Can't checkout diagnosis must contain one and only one diagnose with type principal"
        if len(diagnosis_codes) > 1 or not diagnosis_codes or \
                'principal' not in diagnosis_codes.mapped('diagnosis_type_id.code'):
            raise ValidationError(_(msg))
        res = super(Visit, self.sudo()).action_checkout()
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _to_dict(self, **kwargs):
        """
        A function to convert the object attributes into a dictionary format.

        :param kwargs: additional keyword arguments
        :return: a dictionary containing the converted object attributes
        """
        req_type = kwargs.get('req_type')
        claim_type = kwargs.get('claim_type')
        claim_create_date = (datetime.datetime.now() - datetime.timedelta(days=1))
        days_offset = 1 if self.create_date > claim_create_date else 0
        start_date = self.create_date - datetime.timedelta(days_offset)
        
        visit_product_lines = self.env['emr_op.visit_product_line'].search([('visit_id', '=', self.id)])

        last_end_date = max(
            (line.write_date for line in visit_product_lines), default=None
        )
        if last_end_date:
            end_date = last_end_date + datetime.timedelta(days=1)

        data_dict = {
            "fullUrl": self.get_hl7_reference(self.company_id.base_url),
            "resource": {
                "resourceType": self.profile.display,
                "id": self.id,
                "meta": {
                    "profile": [
                        self.profile.url
                    ]
                },
                "identifier": [
                    {
                        "system": self.request_manager_id.identifier_system,
                        "value": f"{self.profile.display}{self.id}"
                        # "value": self.request_manager_id.identifier
                    }
                ],
                "status": "planned" if req_type == 'Approval' else "finished",
                "class": {
                    "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
                    "code": "IMP" if claim_type == 'institutional' else "AMB",
                    "display": "inpatient encounter" if claim_type == 'institutional' else "ambulatory"
                },
                "subject": {
                    "reference": self.patient_id.get_hl7_reference(base_url=self.company_id.base_url)
                },
                "period": {
                    "start": start_date.strftime('%Y-%m-%dT%H:%M:%S+03:00'),
                    # "end": end_date.strftime('%Y-%m-%dT%H:%M:%S+03:00')
                },
                "serviceProvider": {
                    "reference": self.company_id.get_hl7_reference(base_url=self.company_id.base_url)
                }
            }
        }
        if claim_type == 'institutional':
            data_dict['resource']['serviceType'] = {
                "coding": [
                    {
                        "system": "http://nphies.sa/terminology/CodeSystem/service-type",
                        "code": "sub-acute-care" if req_type == 'Approval' else "acute-care"
                    }
                ]
            }

            data_dict['resource']['hospitalization'] = {
                "extension": [
                    {
                        "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-admissionSpecialty",
                        "valueCodeableConcept": {
                            "coding": [
                                {
                                    "system": "http://nphies.sa/terminology/CodeSystem/practice-codes",
                                    "code": "19.08",
                                    "display": "General Surgery"
                                }
                            ]
                        }
                    },
                ],
                "origin": {
                    "reference": self.company_id.get_hl7_reference(base_url=self.company_id.base_url)
                },
                "admitSource": {
                    "coding": [
                        {
                            "system": "http://nphies.sa/terminology/CodeSystem/admit-source",
                            "code": "WKIN",
                            "display": "Walk-in"
                        }
                    ]
                }
            }

            claim_extension = {
                "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-intendedLengthOfStay",
                "valueCodeableConcept": {
                    "coding": [
                        {
                            "system": "http://nphies.sa/terminology/CodeSystem/intended-length-of-stay",
                            "code": "IO",
                            "display": "Intended overnight"
                        }
                    ]
                }
            }
            
            if req_type == 'Claim':
                data_dict['resource']['hospitalization']['extension'].append(claim_extension)
            
        else:
            data_dict['resource']['period']['end'] = end_date.strftime('%Y-%m-%dT%H:%M:%S+03:00')

            data_dict['resource']['extension'] = [
					{
						"url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-serviceEventType",
						"valueCodeableConcept": {
							"coding": [
								{
									"system": "http://nphies.sa/terminology/CodeSystem/service-event-type",
									"code": "ICSE",
									"display": "Initial client service event"
								}
							]
						}
					}
				]
        
        return data_dict

    # def action_link_with_items(self, records=None):
    #     """To Link Support Info Lines or Communication requests With Visit Product Line Items"""
    #     visit_product_line_id = records._context.get('visit_product_line_id', False)
    #     if visit_product_line_id:
    #         visit_product_line = self.product_line_ids.filtered(lambda line: line.id == visit_product_line_id)
    #         if visit_product_line:
    #             field = 'support_info_line_ids' if records._name == 'emr_op.support_info_line' else 'communication_ids'
    #             visit_product_line[field] += records
    #     else:
    #         raise UserError(_('No Line For Linking Please Select Lines'))
    # endregion
