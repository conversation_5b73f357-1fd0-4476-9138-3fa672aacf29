# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ams_vm
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-11 04:03+0000\n"
"PO-Revision-Date: 2025-03-11 04:03+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__event_count
msgid "# Events"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__meeting_count
msgid "# Meetings"
msgstr ""

#. module: ams_vm
#: model:mail.template,subject:ams_vm.ams_vm_default_email_template
msgid "({{ object.name }}) - {{ object.invitation_id.subject }}"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "/&gt;"
msgstr ""

#. module: ams_vm
#: model:mail.template,body_html:ams_vm.ams_vm_default_email_template
msgid ""
"<html>\n"
"                    <body>\n"
"                        <div style=\"font-family: 'Cairo', sans-serif; direction: rtl; background-color: #f3f4f6; color: #1a1a1a; line-height: 1.8; margin: 0; padding: 0;\">\n"
"                            <div style=\"max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);\">\n"
"                                <div style=\"background: linear-gradient(135deg, #004F23 0%, #007A3B 100%); padding: 40px 20px; text-align: center;\">\n"
"                                    <h1 style=\"color: #ffffff; font-size: 28px; font-weight: 700; margin: 0;\">تصريح زيارة</h1>\n"
"                                </div>\n"
"                                <div style=\"padding: 40px 30px;\">\n"
"                                    <h2 style=\"font-size: 24px; font-weight: 600; color: #004F23; margin-bottom: 20px;\">مرحباً <t t-out=\"object.visitor_id.partner_id.name or ''\"></t>،</h2>\n"
"                                    <p style=\"font-size: 16px;\">يسرنا أن نقدم لكم تفاصيل زيارتكم كما يلي:</p>\n"
"                                    <div style=\"background-color: #f9fafb; border-radius: 12px; padding: 25px; margin: 30px 0; border: 1px solid #dfe3e8;\">\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">رقم التصريح:</strong> <t t-esc=\"object.name\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">قاعة الاجتماع:</strong> <t t-esc=\"object.invitation_id.room_name\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">الموقع:</strong> <t t-esc=\"object.invitation_id.location_address\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">وقت الزيارة:</strong> <t t-esc=\"object.invitation_id._localized_start_date\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">مدة الزيارة:</strong> <t t-esc=\"object.duration_display\"></t>\n"
"                                        </div>\n"
"                                        <div style=\"margin-bottom: 15px;\">\n"
"                                            <strong style=\"color: #8D6E2F;\">وقت الخروج:</strong> <t t-esc=\"object.invitation_id._localized_end_date\"></t>\n"
"                                        </div>\n"
"                                    </div>\n"
"                                    <p style=\"font-size: 16px; margin-top: 30px;\">نشكركم على اهتمامكم. لا تترددوا في التواصل معنا إذا كانت لديكم أي استفسارات.</p>\n"
"                                    <p style=\"font-size: 16px; margin-top: 20px;\">مع أطيب التحيات،<br><strong>إدارة الأمن والسلامة</strong></p>\n"
"                                </div>\n"
"                                <div style=\"background-color: #f9fafb; padding: 30px; text-align: center; border-top: 1px solid #dfe3e8;\">\n"
"                                    <a href=\"https://companywebsite.com\" style=\"color: #004F23; text-decoration: none; font-size: 14px; font-weight: 600; margin: 0 10px;\">زيارة موقعنا</a>\n"
"                                    <a href=\"https://companywebsite.com/contact\" style=\"color: #004F23; text-decoration: none; font-size: 14px; font-weight: 600; margin: 0 10px;\">تواصل معنا</a>\n"
"                                    <p style=\"color: #6c757d; font-size: 12px; margin-top: 20px;\">جميع الحقوق محفوظة ©2024</p>\n"
"                                </div>\n"
"                            </div>\n"
"                        </div>\n"
"                    </body>\n"
"                </html>\n"
"            "
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_invitation_kanban
msgid "<i class=\"fa fa-bookmark mx-1\" title=\"Subject\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-calendar  mx-2\" title=\"Last Response Date\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-calendar  mx-2\" title=\"Request Date\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_invitation_kanban
msgid "<i class=\"fa fa-calendar mx-1\" title=\"Date\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-edit text-primary me-2\" title=\"Response By\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-envelope text-primary mx-2\" title=\"Email Status\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-hashtag text-primary me-2\" title=\"Request Number\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid ""
"<i class=\"fa fa-id-card-o\" style=\"margin-left: 10px;\"/>\n"
"                                    تصريح زيارة"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-tasks text-primary me-2\" title=\"Status\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_invitation_kanban
msgid "<i class=\"fa fa-user mx-1\" title=\"Visitor\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "<i class=\"fa fa-user text-primary me-2\" title=\"Request By\"/>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">الموقع:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">رقم التصريح:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">قاعة الاجتماع:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">مدة الزيارة:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">وقت الخروج:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<span class=\"detail-label\">وقت الزيارة:</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid ""
"<strong><span><i class=\"fa fa-exclamation-triangle\" title=\"Info\"/></span></strong> This request\n"
"                        need to be approved from manager or supervisor <br/> because request time outside allowed time\n"
"                        or approval forced required from manager !!\n"
"                        <span invisible=\"not is_current_user_requester or approval_uid\"><br/> Please select\n"
"                            your <strong>Approval Manager</strong> below and click on button Request\n"
"                            Approval.</span>"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "<strong>ملاحظات هامة:</strong>"
msgstr ""

#. module: ams_vm
#: model:mail.template,name:ams_vm.ams_vm_default_email_template
msgid "AMS VM Default Email Template"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__api_type
msgid "API Type"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_needaction
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_needaction
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_needaction
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_needaction
msgid "Action Needed"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activate
msgid "Activate"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__active
msgid "Active"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__active_lang_count
msgid "Active Lang Count"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_ids
msgid "Activities"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_exception_decoration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_state
msgid "Activity State"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_type_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_type_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_type_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__additional_info
msgid "Additional info"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__type
msgid "Address Type"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__all
msgid "All"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__lang
msgid ""
"All the emails and documents sent to this contact will be translated in this"
" language."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__required_manager_approval
msgid "Always needs manager approval"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__approval_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__approval_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__approval_uid
msgid "Approval Manager"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Approve"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__approved
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__approved
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__approved
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__approved
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Approved"
msgstr ""

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Approved Invitations"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_attachment_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_attachment_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_attachment_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__auto_generate_qr_code
msgid "Auto Generate QR Code"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__auto_generate_qr_code
msgid "Auto generate QR code Per Request"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_1920
msgid "Avatar"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_1024
msgid "Avatar 1024"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_128
msgid "Avatar 128"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_256
msgid "Avatar 256"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__avatar_512
msgid "Avatar 512"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__badge_report_id
msgid "Badge Report Attachment"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__bank_ids
msgid "Banks"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__barcode
msgid "Barcode"
msgstr ""

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_base_request
msgid "Base Visitor Request"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__is_blacklisted
msgid "Blacklist"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_bounce
msgid "Bounce"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__location_address
msgid "Building-Floor"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Cancel"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__cancelled
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__cancelled
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__cancelled
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__cancelled
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Cancelled"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__card_id
msgid "Card"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__channel_ids
msgid "Channels"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__employee
msgid "Check this box if this contact is an Employee."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__city
msgid "City"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__color
msgid "Color"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__commit
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__commit
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__commit
msgid "Comments"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_res_company
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__company_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__company_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__company_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__company_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__company_id
#: model:ir.ui.menu,name:ams_vm.company_menu
msgid "Company"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__company_registry
msgid "Company ID"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__company_name
msgid "Company Name"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__commercial_company_name
msgid "Company Name Entity"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__company_type
msgid "Company Type"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_gid
msgid "Company database ID"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__contact_address
msgid "Complete Address"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__complete_name
msgid "Complete Name"
msgstr ""

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.config_menu
msgid "Configurations"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Confirm"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Confirm Visit"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Duration (Days)"
msgstr ""

#. module: ams_mep
#: model_terms:ir.ui.view,arch_db:ams_mep.invitations_view_form_inherit
msgid "Duration (Hours)"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__confirmed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__confirmed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__confirmed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__confirmed
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Confirmed"
msgstr ""

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Confirmed Invitations"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__child_ids
msgid "Contact"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_form
msgid "Contact Information"
msgstr ""

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.res_partner_menu
msgid "Contacts"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__last_dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__last_dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__last_dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__last_dept_depth
msgid "Contain hierarchy  depth of last department assigned to employee"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__dept_depth
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__dept_depth
msgid "Contain hierarchy of department depth when create record"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__country_id
msgid "Country"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__country_code
msgid "Country Code"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__create_employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__create_employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__create_employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__create_employee_id
msgid "Create By Employee"
msgstr ""

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.visit_action
msgid "Create a visit record"
msgstr ""

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.event_invitation_action
msgid "Create an event invitation"
msgstr ""

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.visit_action
msgid "Create visit records"
msgstr ""

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.visitor_action
msgid "Create visitor"
msgstr ""

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.invitation_action
msgid "Create your first invitation!"
msgstr ""

#. module: ams_vm
#: model_terms:ir.actions.act_window,help:ams_vm.visitor_action
msgid "Create your first visitor!"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__create_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__create_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__create_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__create_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__create_uid
msgid "Created by"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__create_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__create_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__create_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__create_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__create_date
msgid "Created on"
msgstr ""

#. module: ams_vm
#: model:ir.actions.client,name:ams_vm.action_client_dashboard
#: model:ir.ui.menu,name:ams_vm.ams_dashboard_menu
msgid "Dashboard"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__dashboard_up_coming_domain
msgid "Dashboard Upcoming Domain"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__date
msgid "Date"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__department_id
msgid "Department"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__dept_depth
msgid "Department Depth"
msgstr ""

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.menu_hr_department
msgid "Departments"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__display_name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__display_name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__display_name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__display_name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__display_name
msgid "Display Name"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__duration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__duration
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__duration
msgid "Duration"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__duration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__duration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__duration
msgid "Duration in hours"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__partner_share
msgid ""
"Either customer (not a user), either shared user. Indicated the current "
"partner is a customer without access or with a limited access created for "
"sharing data."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__email
msgid "Email"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__email_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__email_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__email_state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__email_state
msgid "Email State"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__email_template_id
msgid "Email Template"
msgstr ""

#. module: ams_vm
#: model:ir.model.constraint,message:ams_vm.constraint_ams_vm_visitor_email_unique
msgid "Email must be unique!"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/invitation_visitor.py:0
#: code:addons/ams_vm/models/invitation_visitor.py:0
msgid "Email template not found"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__employee_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__employee
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__employee_id
msgid "Employee"
msgstr ""

#. module: ams_vm
#: model:res.groups,name:ams_vm.group_employee_request
msgid "Employee Request"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__department_id
msgid "Employee department on record creation"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__employee_ids
#: model:ir.ui.menu,name:ams_vm.menu_hr_employee
msgid "Employees"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__employees_count
msgid "Employees Count"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__end_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__end_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__end_date
msgid "End Date"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__enroll_number
msgid "Enroll Number"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Event"
msgstr ""

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.event_invitation_action
msgid "Event Invitation"
msgstr ""

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.event_invitation_menu
msgid "Event Invitations"
msgstr ""

#. module: ams_vm
#: model:res.groups,name:ams_vm.group_event_manager
msgid "Event Manager"
msgstr ""

#. module: ams_vm
#: model:res.groups,name:ams_vm.group_event_request
msgid "Event Request"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__email_state__failed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__email_state__failed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__email_state__failed
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__email_state__failed
msgid "Failed"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__status__finished
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__status__finished
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__status__finished
msgid "Finished"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__first_name
msgid "First Name"
msgstr ""


#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_follower_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_follower_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_follower_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_follower_ids
msgid "Followers"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_partner_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_partner_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_partner_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__activity_type_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__activity_type_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__activity_type_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__email_formatted
msgid "Formatted Email"
msgstr ""

#. module: ams_vm
#: model:ir.actions.report,name:ams_vm.general_invitation_badge_report_action
msgid "General Invitation Badge"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_latitude
msgid "Geo Latitude"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_longitude
msgid "Geo Longitude"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__date_localization
msgid "Geolocation Date"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Group By"
msgstr ""

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.menu_hr_root
msgid "HR"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__has_approval_access
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__has_approval_access
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__has_approval_access
msgid "Has Approval Access"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__has_message
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__has_message
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__has_message
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__has_message
msgid "Has Message"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__host_work_phone
msgid "Host Work Extension"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__host_work_mobile
msgid "Host Work Mobile"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__id
msgid "ID"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__id_number
msgid "ID Number"
msgstr ""

#. module: ams_vm
#: model:ir.model.constraint,message:ams_vm.constraint_ams_vm_visitor_unique_id_number
msgid "ID Number must be unique!"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__id_type_id
msgid "ID Type"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__im_status
msgid "IM Status"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_exception_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_exception_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_exception_icon
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__activity_exception_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__activity_exception_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__activity_exception_icon
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_needaction
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_needaction
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_needaction
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_has_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_has_sms_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_has_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_has_sms_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_has_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_has_sms_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_has_error
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_1920
msgid "Image"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_1024
msgid "Image 1024"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_128
msgid "Image 128"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_256
msgid "Image 256"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__image_512
msgid "Image 512"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__industry_id
msgid "Industry"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__contact_address_inline
msgid "Inlined Complete Address"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/visitor.py:0
#: code:addons/ams_vm/models/visitor.py:0
msgid ""
"Invalid email address. Please enter a valid email in the correct format."
msgstr ""

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_invitation
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__invitation_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__invitation_id
msgid "Invitation"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_list
msgid "Invitation List"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__invitation_qr_code
msgid "Invitation QR Code"
msgstr ""

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_invitation_visitor
msgid "Invitation Visitor Assignment"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__invitation_visitor_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__invitation_visitor_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__invitation_visitor_ids
msgid "Invitation Visitors"
msgstr ""

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.invitation_action
#: model:ir.actions.server,name:ams_vm.invitation_server_action
#: model:ir.ui.menu,name:ams_vm.invitation_menu
#: model:ir.ui.menu,name:ams_vm.single_invitation_top_menu
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_list
msgid "Invitations"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_calendar
msgid "Invitations Calendar"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__is_current_user_requester
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__is_current_user_requester
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__is_current_user_requester
msgid "Is Current User Requester"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__is_event
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__is_event
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__is_event
msgid "Is Event"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_is_follower
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_is_follower
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_is_follower
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__is_public
msgid "Is Public"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__is_readonly
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__is_readonly
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__is_readonly
msgid "Is Readonly"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__is_company
msgid "Is a Company"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__function
msgid "Job Position"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__lang
msgid "Language"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__last_department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__last_department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__last_department_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__last_department_id
msgid "Last Department"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__last_dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__last_dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__last_dept_depth
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__last_dept_depth
msgid "Last Department Depth"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__last_name
msgid "Last Name"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__write_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__write_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__write_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__write_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__write_uid
msgid "Last Updated by"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__write_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__write_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__write_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__write_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__write_date
msgid "Last Updated on"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__last_department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__last_department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__last_department_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__last_department_id
msgid "Last department assign to employee"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__access_groups_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__access_groups_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__location_address
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__access_groups_ids
msgid "Location"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__visitor_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitor_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__visitor_id
msgid "Main Visitor"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__visitor_id_number
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitor_id_number
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__visitor_id_number
msgid "Main Visitor ID"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__request_max_days
msgid "Maximum number of days for which request can be raised"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__visit_max_hours
msgid "Maximum number of hours for which visit can be raised"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__meeting_ids
msgid "Meetings"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_has_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_has_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_has_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_ids
msgid "Messages"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitor_mobile
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__mobile
msgid "Mobile"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Multiple Days"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/base_visitor_request.py:0
#: code:addons/ams_vm/models/base_visitor_request.py:0
msgid ""
"Multiple Schedule Duration Exceeded: The schedule spans {duration} days, "
"which exceeds the maximum allowed {max_days} days."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__my_activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: ams_vm
#: model:ir.actions.server,name:ams_vm.event_invitation_server_action
msgid "My Event Invitations"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_search
msgid "My Visitors"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__name
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__name
msgid "Name"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__nationality
msgid "Nationality"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__need_approval
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__need_approval
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__need_approval
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__need_approval
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__need_approval
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__need_approval
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__need_approval
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Need Approval"
msgstr ""

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Need Approval Invitations"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_calendar_event_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_date_deadline
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_summary
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_summary
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_summary
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_type_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_type_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_type_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__nextmonth
msgid "Next Month"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__nextweek
msgid "Next Week"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_view_search
msgid "No Show"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/invitation.py:0
#: code:addons/ams_vm/models/invitation.py:0
msgid "No visitors found for the selected invitations."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__email_normalized
msgid "Normalized Email"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__comment
msgid "Notes"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_needaction_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_needaction_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_needaction_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_has_error_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_has_error_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_has_error_counter
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_needaction_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_needaction_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_needaction_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__message_has_error_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__message_has_error_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__message_has_error_counter
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.op_menu
msgid "Operations"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__organization
msgid "Organization"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__parent_name
msgid "Parent name"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__same_company_registry_partner_id
msgid "Partner with same Company Registry"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__same_vat_partner_id
msgid "Partner with same Tax ID"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__payment_token_count
msgid "Payment Token Count"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__payment_token_ids
msgid "Payment Tokens"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__email_state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__status__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__email_state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__status__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__email_state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__email_state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__pending
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__status__pending
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Pending"
msgstr ""

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Pending Invitations"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitor_phone
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone
msgid "Phone"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone_mobile_search
msgid "Phone/Mobile"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/base_visitor_request.py:0
#: code:addons/ams_vm/models/base_visitor_request.py:0
msgid "Please select an approval manager."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__property_product_pricelist
msgid "Pricelist"
msgstr ""

#. module: ams_vm
#: model:ir.actions.server,name:ams_vm.action_server_print_all_visitor_badges
msgid "Print All Visitor Badges"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Print Invitation"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "Profile Photo"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__qr_code
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__qr_code
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__qr_code
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__qr_code
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__qr_code
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "QR Code"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__qr_codes
msgid "Qr Codes"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__rating_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__rating_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__rating_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__rating_ids
msgid "Ratings"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__rec_name
msgid "Rec Name"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__ref
msgid "Reference"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_form
msgid "Regenerate QR Code"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Reject"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__state__rejected
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__state__rejected
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__state__rejected
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__state__rejected
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Rejected"
msgstr ""

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Rejected Invitations"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__parent_id
msgid "Related Company"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__related_company
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__related_company
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__related_company
msgid "Related Organization"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_id
msgid "Related Partner"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__employee_ids
msgid "Related employees based on their private address"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Request Approve"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__request_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__request_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__request_date
msgid "Request Date"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Request Info"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__request_max_days
msgid "Request Max Days"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/res_company.py:0
#: code:addons/ams_vm/models/res_company.py:0
msgid "Request Max Days must be greater than 0."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__name
msgid "Request No"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__request_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__request_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__request_uid
msgid "Requested By"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__required_approval_after_time
msgid "Required Approval After Time"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/res_company.py:0
#: code:addons/ams_vm/models/res_company.py:0
msgid "Required Approval After Time must be between 0 and 23.59"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__required_manager_approval
msgid "Required Manager Approval"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Reset"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__response_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__response_uid
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__response_uid
msgid "Responded By"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__response_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__response_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__response_date
msgid "Response Date"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__activity_user_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__activity_user_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__activity_user_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__room_name
msgid "Room Name"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__status__running
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__status__running
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__status__running
msgid "Running"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__message_has_sms_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__message_has_sms_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__message_has_sms_error
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__user_id
msgid "Salesperson"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__phone_sanitized
msgid "Sanitized Number"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__schedule_type
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__schedule_type
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__schedule_type
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Schedule Type"
msgstr ""

#. module: ams_vm
#: model:res.groups,name:ams_vm.group_security_approval
msgid "Security Approval"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__badge_report_id
msgid "Select default badge report to include in email attachment "
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__email_template_id
msgid "Select email template to be used when sending email"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__visitors_group_id
msgid "Select group to be used for visitors"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__self
msgid "Self"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Send Email"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Send Invitation Email"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_base_request__email_state__sent
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation__email_state__sent
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_invitation_visitor__email_state__sent
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visit__email_state__sent
msgid "Sent"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__sex
msgid "Sex"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__partner_share
msgid "Share Partner"
msgstr ""

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Show More"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__signup_type
msgid "Signup Token Type"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Single Day"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/base_visitor_request.py:0
#: code:addons/ams_vm/models/base_visitor_request.py:0
msgid ""
"Single Schedule Duration Exceeded: The schedule spans {duration:.2f} hours, "
"which exceeds the maximum allowed {max_hours} hours."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__specific_property_product_pricelist
msgid "Specific Property Product Pricelist"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
msgid "Standard"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__starred_message_ids
msgid "Starred Message"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__start_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__start_date
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__start_date
msgid "Start Date"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__state
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__state_id
msgid "State"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__static_map_url
msgid "Static Map Url"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__static_map_url_is_valid
msgid "Static Map Url Is Valid"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__status
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__status
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__status
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_list
msgid "Status"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__activity_state
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__activity_state
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__activity_state
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__street
msgid "Street"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__street2
msgid "Street2"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__subject
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__subject
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__subject
msgid "Subject"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__api_type__suprema
msgid "Suprema"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__synced
msgid "Synced"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__category_id
msgid "Tags"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__vat
msgid "Tax ID"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_form
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_form
msgid "Technical Info"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__user_id
msgid "The internal user in charge of this contact."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__company_registry
msgid ""
"The registry number of the company. Use it if it is different from the Tax "
"ID. It must be unique across all partners of a same country"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_res_company__required_approval_after_time
msgid "Time after which manager approval is required"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__tz
msgid "Timezone"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__tz_offset
msgid "Timezone offset"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__title
msgid "Title"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__today
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
msgid "Today"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__dashboard_up_coming_domain__tomorrow
msgid "Tomorrow"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__total_visits
msgid "Total Visits"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__activity_exception_decoration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__activity_exception_decoration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__activity_exception_decoration
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: ams_vm
#. odoo-javascript
#: code:addons/addons_ams/ams_vm/static/src/dashboard/dashboard.js:0
#: code:addons/ams_vm/static/src/dashboard/dashboard.js:0
msgid "Upcoming Invitations:"
msgstr ""

#. module: ams_vm
#: model:ir.actions.server,name:ams_vm.ir_cron_update_visitor_request_status_ir_actions_server
msgid "Update Visitor Request Status"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__barcode
msgid "Use a barcode to identify this contact."
msgstr ""

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_res_users
msgid "User"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_user_form
msgid "User Classification"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__user_livechat_username
msgid "User Livechat Username"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__user_ids
msgid "Users"
msgstr ""

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_visit
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__visit_id
msgid "Visit"
msgstr ""

#. module: ams_vm
#: model:ir.ui.menu,name:ams_vm.top_menu
msgid "Visit Management"
msgstr ""

#. module: ams_vm
#: model:ir.module.category,description:ams_vm.ams_vm_group_category
#: model:ir.module.category,name:ams_vm.ams_vm_group_category
msgid "Visit Management GROUPS"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__visit_max_hours
msgid "Visit Max Hours"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/res_company.py:0
#: code:addons/ams_vm/models/res_company.py:0
msgid "Visit Max Hours must be between 1 and 24."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__name
msgid "Visit No"
msgstr ""

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_visitor
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__visitor_id
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__is_visitor
#: model:ir.ui.menu,name:ams_vm.visitor_menu
#: model_terms:ir.ui.view,arch_db:ams_vm.base_visitor_request_view_search
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_list
msgid "Visitor"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor__visitor_id_number
msgid "Visitor ID"
msgstr ""

#. module: ams_vm
#. odoo-python
#: code:addons/addons_ams/ams_vm/models/base_visitor_request.py:0
#: code:addons/addons_ams/ams_vm/models/invitation_visitor.py:0
#: code:addons/ams_vm/models/base_visitor_request.py:0
#: code:addons/ams_vm/models/invitation_visitor.py:0
msgid "Visitor Not Found"
msgstr ""

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.visitor_action
#: model:ir.actions.server,name:ams_vm.visitor_server_action
#: model:ir.ui.menu,name:ams_vm.visitor_top_menu
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Visitors"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__visitors_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__visitors_count
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__visitors_count
msgid "Visitors Count"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_res_company__visitors_group_id
msgid "Visitors Group"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.view_res_company_form
msgid "Visitors Management Settings"
msgstr ""

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.visit_action
#: model:ir.ui.menu,name:ams_vm.visit_menu
msgid "Visits"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Visits Count"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "Wave Footer"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__website
msgid "Website Link"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_base_request__website_message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation__website_message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visit__website_message_ids
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__website_message_ids
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__website_message_ids
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__website_message_ids
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__tz
msgid ""
"When printing documents and exporting/importing data, time values are computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your web client."
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__res_company__api_type__zk
msgid "ZKTeco"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__zip
msgid "Zip"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_kanban
msgid "at"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.visitor_view_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_base_request__employee_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation__employee_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visit__employee_id
#: model:ir.model.fields,help:ams_vm.field_ams_vm_visitor__employee_id
msgid "this record owned to this employee as follow up"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "الاسم :"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "الموقع:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "بطاقة دعوة"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "تم إرفاق تصريح الزيارة مع هذا البريد الإلكتروني"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "تواصل معنا"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "جميع الحقوق محفوظة لوزارة الاقتصاد والتخطيط ©2024"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "رقم المرجع:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "قاعة الاجتماع:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "مرحباً"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid ""
"مع أطيب التحيات،<br/>\n"
"                                    <strong>إدارة الأمن والسلامة</strong><br/>\n"
"                                    وزارة الاقتصاد والتخطيط"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "موقعنا الإلكتروني"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "نتطلع لاستقبالكم ونتمنى لكم زيارة موفقة."
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "نرحب بكم في وزارة الاقتصاد والتخطيط. فيما يلي تفاصيل تصريح زيارتكم:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "وقت الخروج:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.general_invitation_badge_template
msgid "وقت الزيارة:"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "يرجى إبراز هذا التصريح عند نقطة الأمن"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.ams_vm_email_template
msgid "يرجى الالتزام بتعليمات الأمن والسلامة"
msgstr ""

#. module: ams_vm
#: model:ir.actions.act_window,name:ams_vm.action_invitation_visitor_import_wizard
msgid "Import Visitors from Excel"
msgstr ""

#. module: ams_vm
#: model:ir.model,name:ams_vm.model_ams_vm_invitation_visitor_import_wizard
msgid "Import Invitation Visitors from Excel"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__excel_file
msgid "Excel File"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__filename
msgid "Filename"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__update_existing
msgid "Update Existing Visitors"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__create_missing
msgid "Create Missing Visitors"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__import_summary
msgid "Import Summary"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__total_rows
msgid "Total Rows"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__created_count
msgid "Created Visitors"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__updated_count
msgid "Updated Visitors"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_invitation_visitor_import_wizard__error_count
msgid "Errors"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,field_description:ams_vm.field_ams_vm_visitor__gender
msgid "Gender"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visitor__gender__male
msgid "Male"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visitor__gender__female
msgid "Female"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields.selection,name:ams_vm.selection__ams_vm_visitor__gender__other
msgid "Other"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Import Visitors from Excel File"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Import Visitors"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Download Template"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Close"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "File Upload"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Import Options"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitation_visitor_import_wizard_view_form
msgid "Import Results"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Import from Excel"
msgstr ""

#. module: ams_vm
#: model_terms:ir.ui.view,arch_db:ams_vm.invitations_view_form
msgid "Visitors List"
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "openpyxl library is not installed. Please install it to use Excel import."
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Please upload an Excel file."
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Error processing Excel file: %s"
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Could not find valid headers in Excel file. Expected headers: %s"
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Missing required fields: %s"
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Invalid email format: %s"
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Visitor with ID %s not found and creation is disabled"
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Error processing visitor %s: %s"
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Email %s already exists for visitor %s"
msgstr ""

#. module: ams_vm
#: code:addons/ams_vm/wizards/invitation_visitor_import_wizard.py:0
msgid "Error creating template file: %s"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation_visitor_import_wizard__excel_file
msgid "Upload Excel file with visitor data"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation_visitor_import_wizard__update_existing
msgid "Update existing visitors if found by ID number"
msgstr ""

#. module: ams_vm
#: model:ir.model.fields,help:ams_vm.field_ams_vm_invitation_visitor_import_wizard__create_missing
msgid "Create new visitors if not found"
msgstr ""

