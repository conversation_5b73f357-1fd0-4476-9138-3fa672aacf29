# -*- coding: utf-8 -*-
import uuid
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import re

class Visitor(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_vm.visitor'
    _description = 'Visitor'
    _inherit = ['ams_base.abstract_model', 'mail.thread', 'mail.activity.mixin']
    _inherits = {'res.partner': 'partner_id'}
    _sql_constraints = [
        ('email_unique', 'unique(email)', 'Email must be unique!'),
        ('unique_id_number', 'unique(id_number)', 'ID Number must be unique!')

    ]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    def _generate_unique_numeric_qr_code(self):
        while True:
            qr_code = uuid.uuid4().int & (1 << 64) - 1
            # Check if the number already exists in the database
            if not self.search([('qr_code', '=', qr_code)]):
                return qr_code
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    enroll_number = fields.Integer(string='Enroll Number', tracking=True, default=0, copy=False)
    id_number = fields.Char(string="ID Number" , required=True)
    qr_code = fields.Char(string='QR Code', copy=False, default=lambda self: self._generate_unique_numeric_qr_code(),readonly=True)
    email = fields.Char(string='Email', tracking=True , required=True,related='partner_id.email',store=True,readonly=False)
    first_name = fields.Char(string='First Name')
    last_name = fields.Char(string='Last Name')
    gender = fields.Selection([
        ('male', 'Male'),
        ('female', 'Female'),
        ('other', 'Other')
    ], string='Gender')

    # endregion

    # region  Special
    # endregion

    # region  Relational
    partner_id = fields.Many2one('res.partner', string='Related Partner', required=True, ondelete='cascade')
    card_id = fields.Many2one('ams.card', string='Card')
    synced = fields.Boolean(string="Synced", related='card_id.user_id.synced', readonly=True,store=True)
    activate = fields.Boolean(string="Activate", related='card_id.user_id.activate', readonly=True,store=True)
    company_id = fields.Many2one('res.company', default=lambda self: self.env.user.company_id, string="Company")
    organization = fields.Char(string="Organization",required=True)



    # endregion

    # region  Computed
    color = fields.Char(string="Color",compute="_compute_color")


    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('activate')
    def _compute_color(self):
        for record in self:
            record.color = 'green' if record.activate else 'red'
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.constrains('email')
    def _check_email_format(self):
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        for record in self:
            if record.email and not re.match(email_regex, record.email):
                raise ValidationError(_("Invalid email address. Please enter a valid email in the correct format."))


    @api.onchange('first_name', 'last_name')
    def _onchange_name(self):
        if self.first_name and self.last_name:
            self.name = f"{self.first_name} {self.last_name}"


    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def create(self, vals):

        if isinstance(vals, list):
            for val in vals:
                if val.get('enroll_number', 0) == 0:
                    val['enroll_number'] = int(self.env['ir.sequence'].next_by_code('ams_vm.seq_user') or 0)
        else:
            if vals.get('enroll_number', 0) == 0:
                vals['enroll_number'] = int(self.env['ir.sequence'].next_by_code('ams_vm.seq_user') or 0)

        res = super(Visitor, self).create(vals)
        return res
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_regenerate_qr_code(self):
        self.card_id = None
        self.qr_code = self._generate_unique_numeric_qr_code()


    def action_copy_id_from_partner(self):
        for record in self:
            if record.partner_id.id_number:
                record.id_number = record.partner_id.id_number
            else:
                record.id_number = record.id_number


    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    #endregion
