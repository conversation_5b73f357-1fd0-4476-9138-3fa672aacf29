# -*- coding: utf-8 -*-

{
    "name": "AMS - Visit Management",
    "version": "1.0.3",
    "depends": [
        'base', 'mail', 'ams'
    ],
    "author": "laplacesoftware",
    "category": "AMS",
    "website": "https://www.laplacesoftware.com/",
    "images": ["static/description/images/main_screenshot.jpg"],
    "price": "0",
    "license": "OPL-1",
    "currency": "USD",
    "summary": "Access Management System Base Module",
    "description": """
    used to share common models between other custom modules


""",
    "data": [
        "security/groups.xml",
        "security/ir.model.access.csv",
        "security/security.xml",

        "views/dashboard.xml",
        # "reports/invitation_report_views.xml",
        "reports/general_invitation_badge_report_views.xml",

        "data/default_data.xml",
        "data/invitation_sequence.xml",
        "data/visit_sequence.xml",
        "data/ams_user_sequence.xml",
        "data/email_template_data.xml",
        "data/cron_data.xml",
        "views/email_template.xml",

        "views/base_visitor_request_views.xml",

        "wizards/invitation_visitor_import_wizard_views.xml",
        "views/visit_views.xml",
        "views/invitation_views.xml",
        "views/res_user.xml",
        "views/res_company_views.xml",
        "views/visitor_views.xml",
        "views/menu.xml",


    ],
    'assets': {
        'web.assets_backend': [
            'ams_vm/static/src/dashboard/*',

        ],

        'web.assets_common': [],
    },
    "installable": True,
    "auto_install": False,
    "application": True,
}
