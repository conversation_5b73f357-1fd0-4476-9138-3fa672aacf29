# -*- coding: utf-8 -*-

import base64
import io
import logging
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError

try:
    from openpyxl import load_workbook
except ImportError:
    load_workbook = None

_logger = logging.getLogger(__name__)


class InvitationVisitorImportWizard(models.TransientModel):
    _name = 'ams_vm.invitation_visitor_import_wizard'
    _description = 'Import Invitation Visitors from Excel'

    # region ---------------------- Fields Declaration ---------------------------------
    invitation_id = fields.Many2one('ams_vm.invitation', string='Invitation', required=True)
    excel_file = fields.Binary(string='Excel File',
                              help='Upload Excel file with visitor data')
    filename = fields.Char(string='Filename')
    
    # Import options
    update_existing = fields.Boolean(string='Update Existing Visitors', default=True,
                                   help='Update existing visitors if found by ID number')
    create_missing = fields.Boolean(string='Create Missing Visitors', default=True,
                                  help='Create new visitors if not found')
    
    # Results
    import_summary = fields.Text(string='Import Summary', readonly=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('imported', 'Imported')
    ], default='draft')
    
    # Statistics
    total_rows = fields.Integer(string='Total Rows', readonly=True)
    created_count = fields.Integer(string='Created Visitors', readonly=True)
    updated_count = fields.Integer(string='Updated Visitors', readonly=True)
    error_count = fields.Integer(string='Errors', readonly=True)
    # endregion

    # region ---------------------- Action Methods -------------------------------------
    def action_import_visitors(self):
        """Main import action"""
        self.ensure_one()
        
        if not load_workbook:
            raise UserError(_('openpyxl library is not installed. Please install it to use Excel import.'))
        
        if not self.excel_file:
            raise UserError(_('Please upload an Excel file.'))
        
        try:
            # Decode and load Excel file
            file_data = base64.b64decode(self.excel_file)
            workbook = load_workbook(io.BytesIO(file_data))
            worksheet = workbook.active
            
            # Process the Excel data
            result = self._process_excel_data(worksheet)
            
            # Update wizard with results
            self.write({
                'state': 'imported',
                'import_summary': result['summary'],
                'total_rows': result['total_rows'],
                'created_count': result['created_count'],
                'updated_count': result['updated_count'],
                'error_count': result['error_count'],
            })
            
            return {
                'type': 'ir.actions.act_window',
                'res_model': self._name,
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
                'context': {'default_state': 'imported'}
            }
            
        except Exception as e:
            _logger.error(f"Error importing visitors: {str(e)}")
            raise UserError(_('Error processing Excel file: %s') % str(e))

    def action_close(self):
        """Close wizard and refresh invitation form"""
        return {
            'type': 'ir.actions.act_window_close',
        }

    def action_download_template(self):
        """Download Excel template file"""
        if not load_workbook:
            raise UserError(_('openpyxl library is not installed. Please install it to use Excel import.'))

        try:
            # Create a new workbook with sample data
            from openpyxl import Workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Visitors"

            # Add headers
            headers = ['first_name', 'last_name','id_type_id', 'id_number', 'email', 'organization', 'nationality', 'mobile', 'sex']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # Add sample data
            sample_data = [
                ['Fahd', 'Abdullah','National ID', '1123456789', '<EMAIL>', 'ABC Company', 'Saudi Arabia', '00966512345678', 'Male'],
                ['Sara', 'Ahmed','Resident ID', '2987654321', '<EMAIL>', 'XYZ Corp', 'Egypt', '00966512345679', 'Female'],
                ['Ahmed', 'Saleh','Passport', '*********', '<EMAIL>', 'XYZ Corp', 'Egypt', '00966512345677', 'Male'],
            ]

            for row_idx, row_data in enumerate(sample_data, 2):
                for col_idx, value in enumerate(row_data, 1):
                    ws.cell(row=row_idx, column=col_idx, value=value)

            # Save to bytes
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            # Encode to base64
            file_data = base64.b64encode(output.read())

            # Create attachment
            attachment = self.env['ir.attachment'].create({
                'name': 'visitors_import_template.xlsx',
                'type': 'binary',
                'datas': file_data,
                'res_model': self._name,
                'res_id': self.id,
                'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })

            return {
                'type': 'ir.actions.act_url',
                'url': f'/web/content/{attachment.id}?download=true',
                'target': 'self',
            }

        except Exception as e:
            _logger.error(f"Error creating template: {str(e)}")
            raise UserError(_('Error creating template file: %s') % str(e))
    # endregion

    # region ---------------------- Business Methods -------------------------------------
    def _process_excel_data(self, worksheet):
        """Process Excel worksheet data"""
        visitors_data = []
        errors = []
        
        # Expected column headers (case insensitive)
        expected_headers = {
            'first_name': ['first_name', 'first name', 'firstname'],
            'last_name': ['last_name', 'last name', 'lastname'],
            'id_type_id': ['id_type_id', 'id type'],
            'id_number': ['id_number', 'id number', 'id_no', 'national_id'],
            'email': ['email', 'email_address'],
            'organization': ['organization', 'company', 'org'],
            'nationality': ['nationality', 'country'],
            'mobile': ['mobile', 'phone', 'mobile_phone'],
            'sex': ['sex', 'gender']

        }
        
        # Find header row and map columns
        header_mapping = self._find_headers(worksheet, expected_headers)
        if not header_mapping:
            raise UserError(_('Could not find valid headers in Excel file. Expected headers: %s') % 
                          ', '.join(expected_headers.keys()))
        
        # Process data rows
        row_count = 0
        for row_num, row in enumerate(worksheet.iter_rows(min_row=2, values_only=True), start=2):
            if not any(row):  # Skip empty rows
                continue
                
            row_count += 1
            try:
                visitor_data = self._extract_visitor_data(row, header_mapping, row_num)
                if visitor_data:
                    visitors_data.append(visitor_data)
            except Exception as e:
                errors.append(f"Row {row_num}: {str(e)}")
        
        # Create/update visitors
        created_count = 0
        updated_count = 0

        for visitor_data in visitors_data:
            try:
                # Use savepoint for each visitor to avoid transaction rollback
                with self.env.cr.savepoint():
                    result = self._create_or_update_visitor(visitor_data)
                    if result['created']:
                        created_count += 1
                    else:
                        updated_count += 1
            except Exception as e:
                error_msg = f"Visitor {visitor_data.get('id_number', 'Unknown')}: {str(e)}"
                _logger.error(error_msg)
                errors.append(error_msg)
        
        # Generate summary
        summary_lines = [
            f"Import completed successfully!",
            f"Total rows processed: {row_count}",
            f"Visitors created: {created_count}",
            f"Visitors updated: {updated_count}",
            f"Errors: {len(errors)}"
        ]
        
        if errors:
            summary_lines.append("\nErrors:")
            summary_lines.extend(errors[:10])  # Show first 10 errors
            if len(errors) > 10:
                summary_lines.append(f"... and {len(errors) - 10} more errors")
        
        return {
            'summary': '\n'.join(summary_lines),
            'total_rows': row_count,
            'created_count': created_count,
            'updated_count': updated_count,
            'error_count': len(errors)
        }

    def _find_headers(self, worksheet, expected_headers):
        """Find and map column headers"""
        header_mapping = {}
        
        # Check first few rows for headers
        for row in worksheet.iter_rows(max_row=5, values_only=True):
            if not any(row):
                continue
                
            # Convert row to lowercase for comparison
            row_lower = [str(cell).lower().strip() if cell else '' for cell in row]
            
            # Try to match headers
            temp_mapping = {}
            for field, possible_names in expected_headers.items():
                for col_idx, cell_value in enumerate(row_lower):
                    if cell_value in possible_names:
                        temp_mapping[field] = col_idx
                        break
            
            # If we found at least required fields, use this mapping
            required_fields = ['first_name', 'last_name', 'id_number', 'email']
            if all(field in temp_mapping for field in required_fields):
                header_mapping = temp_mapping
                break
        
        return header_mapping

    def _extract_visitor_data(self, row, header_mapping, row_num):
        """Extract visitor data from Excel row"""
        visitor_data = {}
        
        # Extract mapped fields
        for field, col_idx in header_mapping.items():
            if col_idx < len(row):
                value = row[col_idx]
                if value is not None:
                    visitor_data[field] = str(value).strip()
        
        # Validate required fields
        required_fields = ['first_name', 'last_name', 'id_number', 'email']
        missing_fields = [field for field in required_fields if not visitor_data.get(field)]
        if missing_fields:
            raise ValidationError(_('Missing required fields: %s') % ', '.join(missing_fields))

        # Validate email format
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, visitor_data['email']):
            raise ValidationError(_('Invalid email format: %s') % visitor_data['email'])

        return visitor_data

    def _create_or_update_visitor(self, visitor_data):
        """Create or update visitor and add to invitation"""
        try:
            # Check if visitor exists by id_number
            existing_visitor = self.env['ams_vm.visitor'].search([
                ('email', '=', visitor_data['email'])
            ], limit=1)

            created = False

            if existing_visitor:
                if self.update_existing:
                    # Update existing visitor
                    self._update_visitor(existing_visitor, visitor_data)
                visitor = existing_visitor
            else:
                if self.create_missing:
                    # Create new visitor
                    visitor = self._create_visitor(visitor_data)
                    created = True
                else:
                    raise UserError(_('Visitor with ID %s not found and creation is disabled') %
                                  visitor_data['id_number'])

            # Add visitor to invitation if not already added
            existing_invitation_visitor = self.env['ams_vm.invitation_visitor'].search([
                ('invitation_id', '=', self.invitation_id.id),
                ('visitor_id', '=', visitor.id)
            ], limit=1)

            if not existing_invitation_visitor:
                self.env['ams_vm.invitation_visitor'].create({
                    'invitation_id': self.invitation_id.id,
                    'visitor_id': visitor.id,
                    'visitor_id_number': visitor.id_number,
                })

            return {'created': created, 'visitor': visitor}

        except Exception as e:
            _logger.error(f"Error in _create_or_update_visitor for ID {visitor_data.get('id_number')}: {str(e)}")
            # Re-raise with more context
            raise UserError(_('Error processing visitor %s: %s') % (visitor_data.get('id_number', 'Unknown'), str(e)))

    def _create_visitor(self, visitor_data):
        """Create new visitor"""
        try:
            # Check for duplicate email first
            existing_email = self.env['ams_vm.visitor'].search([
                ('email', '=', visitor_data['email'])
            ], limit=1)
            if existing_email:
                raise UserError(_('Email %s already exists for visitor %s') %
                              (visitor_data['email'], existing_email.name))

            # Prepare partner data
            partner_vals = {
                'name': f"{visitor_data['first_name']} {visitor_data['last_name']}",
                'email': visitor_data['email'],
                'mobile': visitor_data.get('mobile', ''),
                'is_company': False,
            }

            # Handle nationality/country
            if visitor_data.get('nationality'):
                country = self.env['res.country'].search([
                    ('name', 'ilike', visitor_data['nationality'])
                ], limit=1)
                if country:
                    partner_vals['country_id'] = country.id


            if visitor_data.get('id_type_id'):
                id_type = self.env['ams.id_type'].search([
                    ('name', 'ilike', visitor_data['id_type_id'])
                ], limit=1)
                if id_type:
                    partner_vals['id_type_id'] = id_type.id

            # Create partner first
            partner = self.env['res.partner'].create(partner_vals)

            # Create visitor
            visitor_vals = {
                'partner_id': partner.id,
                'first_name': visitor_data['first_name'],
                'last_name': visitor_data['last_name'],
                'id_number': visitor_data['id_number'],
                'email': visitor_data['email'],
                'organization': visitor_data.get('organization', ''),
                'nationality':partner.country_id.id if partner.country_id else False
            }

            # Handle gender field
            if visitor_data.get('sex'):
                gender_mapping = {
                    'male': 'male',
                    'female': 'female',
                    'm': 'male',
                    'f': 'female',
                    'man': 'male',
                    'woman': 'female',
                }
                gender_value = visitor_data['sex'].lower().strip()
                visitor_vals['gender'] = gender_mapping.get(gender_value, 'other')

            return self.env['ams_vm.visitor'].create(visitor_vals)

        except Exception as e:
            _logger.error(f"Error creating visitor: {str(e)}")
            _logger.error(f"Visitor data: {visitor_data}")
            raise

    def _update_visitor(self, visitor, visitor_data):
        """Update existing visitor"""
        # Update visitor fields
        visitor_vals = {
            'first_name': visitor_data['first_name'],
            'last_name': visitor_data['last_name'],
            'email': visitor_data['email'],
            'id_number': visitor_data['id_number'],
            'organization': visitor_data.get('organization', visitor.organization),
        }

        # Handle gender field
        if visitor_data.get('sex'):
            gender_mapping = {
                'male': 'male',
                'female': 'female',
                'm': 'male',
                'f': 'female',
                'man': 'male',
                'woman': 'female',
            }
            gender_value = visitor_data['sex'].lower().strip()
            visitor_vals['gender'] = gender_mapping.get(gender_value, 'other')
        
        # Update partner fields
        partner_vals = {
            'name': f"{visitor_data['first_name']} {visitor_data['last_name']}",
            'email': visitor_data['email'],
            'mobile': visitor_data.get('mobile', visitor.partner_id.mobile),
            'id_number': visitor_data['id_number'],
        }
        
        # Handle nationality/country
        if visitor_data.get('nationality'):
            country = self.env['res.country'].search([
                ('name', 'ilike', visitor_data['nationality'])
            ], limit=1)
            if country:
                partner_vals['country_id'] = country.id
                visitor_vals['nationality'] = country.id
        
        visitor.write(visitor_vals)
        visitor.partner_id.write(partner_vals)
    # endregion
