---Models in module 'calendar'---
new model calendar.provider.config [transient]
# NOTHING TO DO: new model
---Fields in module 'calendar'---
calendar     / calendar.event           / access_token (char)           : NEW
# NOTIHNG TO DO: new feature
calendar     / calendar.event           / res_id (integer)              : relation is now 'res_model' ('False') [nothing to do]
calendar     / calendar.event           / res_id (integer)              : type is now 'many2one_reference' ('integer')
# NOTIHNG TO DO: db layout is not modified
calendar     / calendar.event           / videocall_channel_id (many2one): NEW relation: mail.channel
calendar     / calendar.event           / videocall_location (char)     : now a function
# NOTIHNG TO DO: new feature
---XML records in module 'calendar'---
DEL ir.actions.act_window: calendar.action_calendar_event_notify
NEW ir.model.access: calendar.access_calendar_provider_config_all
NEW ir.model.access: calendar.access_calendar_provider_config_manager
NEW ir.ui.view: calendar.calendar_provider_config_view_form
# NOTHING TO DO: no extra changes in DB needed.
