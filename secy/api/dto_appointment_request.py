from odoo.addons.secy.api.dto_contact import ContactDTO
from .utils import *
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast


@dataclass
class AppointmentRequestDTO:
    """Convert all data contract to field match python name convention
        all date or date time map as string in format
        DATETIME_DISPLAY_FORMAT = "dd/MM/yyyy HH:mm:ss"
        DATE_DISPLAY_FORMAT = "dd/MM/yyyy"
        selection field or enum will be integer field
        """
    appointment_request_id: str = "0"
    appointment_request_contact: ContactDTO = None
    description: str = ""
    subject: str = ""
    appointment_request_time: str = ""
    appointment_request_date: str = ""
    appointment_request_status: int = 0
    attachment_data: str = ""
    attachment_size: int = 0
    appointment_request_date_hj: str = ""

    @staticmethod
    def from_dict(obj: Any) -> 'AppointmentRequestDTO':
        """used as expected input from api and when need to convert it to Model DTO"""
        assert isinstance(obj, dict)
        appointment_request_id = obj.get("AppointmentRequestID")
        appointment_request_contact =obj.get("AppointmentRequestContact")
        description = obj.get("Description")
        subject = obj.get("Subject")
        appointment_request_time = obj.get("AppointmentRequestTime")
        appointment_request_date = obj.get("AppointmentRequestDate")
        appointment_request_status = obj.get("AppointmentRequestStatus")
        attachment_data = obj.get("AttachmentData")
        attachment_size = obj.get("AttachmentSize")
        appointment_request_date_hj = obj.get("AppointmentRequestDateHJ")

        return AppointmentRequestDTO(
            appointment_request_id,
            appointment_request_contact,
            description,
            subject,
            appointment_request_time,
            appointment_request_date,
            appointment_request_status,
            attachment_data,
            attachment_size,
            appointment_request_date_hj
        )


    def to_dict(self) -> dict:
        """used in api when need to convert Model DTO to dict to return json data in request library"""
        result: dict = {}
        result["AppointmentRequestID"] = from_str(self.appointment_request_id)
        result[
            "AppointmentRequestContact"] = self.appointment_request_contact.to_dict() if self.appointment_request_contact else {}
        result["Description"] = from_str(self.description)
        result["Subject"] = from_str(self.subject)
        result["AppointmentRequestTime"] = from_str(self.appointment_request_time)
        result["AppointmentRequestDate"] = from_str(self.appointment_request_date)
        result["AppointmentRequestStatus"] = from_int(self.appointment_request_status)
        result["AttachmentData"] = from_str(self.attachment_data)
        result["AttachmentSize"] = from_int(self.attachment_size)
        result["AppointmentRequestDateHJ"] = from_str(self.appointment_request_date_hj)
        return result