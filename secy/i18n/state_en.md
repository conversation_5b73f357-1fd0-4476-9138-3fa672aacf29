#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.draft"
msgstr "draft"

#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.planned"
msgstr "planned"

#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.canceled"
msgstr "Canceled"

#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.delayed"
msgstr "Delayed"

#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.admitted"
msgstr "Admitted"

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.draft"
msgstr "Draft"

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.requested"
msgstr "Requested"

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.approved"
msgstr "Approved"

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.declined"
msgstr "Declined"

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.admitted"
msgstr "Admitted"

#. module: secy
#: code:addons_secy/models/invitation.py:0
#, python-format
msgid "secy.invitation.draft"
msgstr "Draft"

#. module: secy
#: code:addons_secy/models/invitation.py:0
#, python-format
msgid "secy.invitation.requested"
msgstr "Requested"

#. module: secy
#: code:addons_secy/models/invitation.py:0
#, python-format
msgid "secy.invitation.accepted"
msgstr "Accepted"

#. module: secy
#: code:addons_secy/models/invitation.py:0
#, python-format
msgid "secy.invitation.canceled"
msgstr "Canceled"

#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.draft"
msgstr "Draft"

#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.waiting"
msgstr "Waiting"

#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.approved"
msgstr "Approved"

#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.declined"
msgstr "Declined"

#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.admitted"
msgstr "Admitted"

#. module: secy
#: code:addons_secy/models/memo.py:0
#, python-format
msgid "secy.memo.new"
msgstr "New"

#. module: secy
#: code:addons_secy/models/memo.py:0
#, python-format
msgid "secy.memo.reviewed"
msgstr "Reviewed"

#. module: secy
#: code:addons_secy/models/memo.py:0
#, python-format
msgid "secy.memo.removed"
msgstr "Removed"

#. module: secy
#: code:addons_secy/models/alert.py:0
#, python-format
msgid "secy.alert.new"
msgstr "New"

#. module: secy
#: code:addons_secy/models/alert.py:0
#, python-format
msgid "secy.alert.read"
msgstr "Read"

#. module: secy
#: code:addons_secy/models/alert.py:0
#, python-format
msgid "secy.alert.deleted"
msgstr "Deleted"

****pot****


#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.planned"
msgstr ""

#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.canceled"
msgstr ""

#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.delayed"
msgstr ""

#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.admitted"
msgstr ""

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.draft"
msgstr ""

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.requested"
msgstr ""

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.approved"
msgstr ""

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.declined"
msgstr ""

#. module: secy
#: code:addons_secy/models/appointment_request.py:0
#, python-format
msgid "secy.appointment_request.admitted"
msgstr ""

#. module: secy
#: code:addons_secy/models/invitation.py:0
#, python-format
msgid "secy.invitation.draft"
msgstr ""

#. module: secy
#: code:addons_secy/models/invitation.py:0
#, python-format
msgid "secy.invitation.requested"
msgstr ""

#. module: secy
#: code:addons_secy/models/invitation.py:0
#, python-format
msgid "secy.invitation.accepted"
msgstr ""

#. module: secy
#: code:addons_secy/models/invitation.py:0
#, python-format
msgid "secy.invitation.canceled"
msgstr ""

#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.waiting"
msgstr ""

#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.approved"
msgstr ""

#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.declined"
msgstr ""

#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.admitted"
msgstr ""

#. module: secy
#: code:addons_secy/models/memo.py:0
#, python-format
msgid "secy.memo.new"
msgstr ""

#. module: secy
#: code:addons_secy/models/memo.py:0
#, python-format
msgid "secy.memo.reviewed"
msgstr ""

#. module: secy
#: code:addons_secy/models/memo.py:0
#, python-format
msgid "secy.memo.removed"
msgstr ""

#. module: secy
#: code:addons_secy/models/alert.py:0
#, python-format
msgid "secy.alert.new"
msgstr ""

#. module: secy
#: code:addons_secy/models/alert.py:0
#, python-format
msgid "secy.alert.read"
msgstr ""

#. module: secy
#: code:addons_secy/models/alert.py:0
#, python-format
msgid "secy.alert.deleted"
msgstr ""


#. module: secy
#: code:addons_secy/models/visit.py:0
#, python-format
msgid "secy.visit.draft"
msgstr ""

#. module: secy
#: code:addons_secy/models/appointement.py:0
#, python-format
msgid "secy.appointment.draft"
msgstr ""


