<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Leaflet Map View Definition -->
        <record id="view_map_location_leaflet" model="ir.ui.view">
            <field name="name">map.location.leaflet</field>
            <field name="model">map.location</field>
            <field name="type">leaflet_map</field>
            <field name="arch" type="xml">
                <leaflet_map string="Map Locations"
                             field_latitude="latitude"
                             field_longitude="longitude"
                             field_title="name"
                             limit="100"
                             panel_title="Map Locations Panel"
                             hideAddress="True"
                             hideName="True"
                             hideTitle="True"
                >
                    <field name="name"/>
                    <field name="latitude"/>
                    <field name="longitude"/>
                </leaflet_map>

            </field>
        </record>

        <!-- list  View Definition -->
        <record id="view_map_location_list" model="ir.ui.view">
            <field name="name">map.location.list</field>
            <field name="model">map.location</field>
            <field name="arch" type="xml">
                <list>
                    <field name="name"/>
                    <field name="latitude"/>
                    <field name="longitude"/>
                </list>
            </field>
        </record>


        <!-- Form View Definition -->
        <record id="view_map_location_form" model="ir.ui.view">
            <field name="name">map.location.form</field>
            <field name="model">map.location</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="latitude"/>
                            <field name="longitude"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Search View Definition (Optional) -->
        <record id="view_map_location_search" model="ir.ui.view">
            <field name="name">map.location.search</field>
            <field name="model">map.location</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name"/>
                    <field name="latitude"/>
                    <field name="longitude"/>
                </search>
            </field>
        </record>


        <!-- Action for the Tree View -->
        <record id="action_map_location" model="ir.actions.act_window">
            <field name="name">Map Locations</field>
            <field name="res_model">map.location</field>
            <field name="view_mode">list,form,leaflet_map,search</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to create a new map location.
                </p>
            </field>
        </record>

    </data>
</odoo>